# AutoSub with LLM - Advanced Subtitle Generation Pipeline

## 🎯 Overview

AutoSub with LLM is a comprehensive TTS data pipeline variant that generates high-quality SRT subtitle files with LLM-enhanced transcription post-processing. It combines IBM Granite Speech model for superior ASR with either Google Gemini or local Aya Expanse LLMs for contextual transcription enhancement.

## 🚀 Key Features

### Core Pipeline
- **IBM Granite Speech Model**: High-quality ASR transcription using Granite-speech-3.3-2b
- **LLM Enhancement**: Post-processing with Google Gemini or local Aya Expanse
- **SRT Generation**: Precise video-synchronized subtitle files
- **Step-Sequential Processing**: Memory-efficient batch processing
- **Video Synchronization**: Accurate timing preservation from original video

### Advanced Capabilities
- **Dual LLM Support**: Cloud (Gemini) or local (Aya Expanse) options with custom model configuration
- **Multilingual Excellence**: Comprehensive support for Vietnamese, Chinese, Japanese, Korean, Thai, French, German, Spanish, Italian, Portuguese, Russian, and English
- **Batch Processing**: Contextual understanding across transcript segments with language-aware enhancement
- **Speaker Diarization**: Multi-speaker support with speaker-specific SRTs
- **Quality Enhancement**: Advanced ASR error correction, diacritic restoration, and punctuation improvement
- **Cultural Context**: Preserves idioms, honorifics, and language-specific expressions
- **Multiple Output Formats**: Combined, speaker-specific, and comparison SRTs

## 📋 Pipeline Structure

The pipeline follows this sequence, skipping the cleaning step for subtitle generation:

```
01_preprocessed    → Video-to-audio conversion and normalization
02_diarization     → Speaker diarization and segmentation  
03_segments        → Audio segments from diarization
05_denoised        → Enhanced audio quality (skips 04_cleaned)
06_transcripts     → IBM Granite Speech transcriptions
07_llm_enhanced    → LLM post-processed transcriptions
08_srt_output      → Final SRT subtitle files
```

## 🌍 Multilingual Support

AutoSub with LLM provides comprehensive multilingual support with language-specific enhancement rules:

### Asian Languages
- **Vietnamese**: Full diacritic restoration (á, à, ả, ã, ạ, đ, etc.), consonant cluster correction, honorific preservation
- **Chinese**: Traditional/Simplified character support, tone marker handling, proper grammar structure
- **Japanese**: Hiragana/Katakana/Kanji restoration, particle correction, keigo (honorific) preservation
- **Korean**: Hangul restoration, romanization error fixing, speech level maintenance
- **Thai**: Thai script restoration, tone marker correction, proper word spacing

### European Languages
- **French**: Accent restoration (é, è, ê, ç, etc.), liaison/elision correction, formal/informal address
- **German**: Umlaut restoration (ä, ö, ü, ß), compound word handling, case system correction
- **Spanish**: Accent restoration (ñ, á, é, etc.), gender agreement, regional variation support
- **Italian**: Accent restoration, double consonant correction, verb conjugation fixing
- **Portuguese**: Nasal sound correction (ão, ões), Brazilian vs European Portuguese support
- **Russian**: Cyrillic restoration, case system handling, aspect pair preservation

### Mixed Language Support
- **Code-switching**: Natural preservation of language mixing patterns
- **Technical Terms**: Maintains technical vocabulary in appropriate languages
- **Cultural Context**: Preserves idioms, expressions, and cultural references

## 🛠️ Installation

### Prerequisites

1. **Python 3.8+** with pip
2. **CUDA-capable GPU** (recommended for optimal performance)
3. **FFmpeg** (for video processing)

### Installation with UV (Recommended)

UV is a fast Python package manager. Install dependencies with:

```bash
# Install UV if you haven't already
curl -LsSf https://astral.sh/uv/install.sh | sh

# Install all AutoSub dependencies
uv add torch torchaudio --index-url https://download.pytorch.org/whl/cu118
uv add transformers accelerate peft soundfile
uv add pydub librosa
uv add google-generativeai
uv add llama-cpp-python
uv add pyannote.audio
uv add tqdm colorama
uv add pandas numpy
uv add pyyaml pathlib2
uv add moviepy
uv add resemble-enhance

# Or install from requirements file
uv add -r requirements-autosub.txt
```

### Alternative Installation with pip

```bash
# Core dependencies
pip install torch torchaudio --index-url https://download.pytorch.org/whl/cu118
pip install transformers accelerate peft soundfile
pip install pydub librosa
pip install google-generativeai  # For Gemini LLM
pip install llama-cpp-python     # For Aya Expanse LLM
pip install pyannote.audio
pip install tqdm colorama
pip install pandas numpy
pip install pyyaml pathlib2
pip install moviepy
pip install resemble-enhance

# Or install from requirements file
pip install -r requirements-autosub.txt
```

### Optional Dependencies

```bash
# For enhanced audio processing
pip install resemble-enhance

# For additional video format support
pip install moviepy
```

## ⚙️ Configuration

### 1. Copy Configuration Template

```bash
cp autosub-config.yaml config.yaml
```

### 2. Configure Authentication

Edit `config.yaml` and set:

```yaml
# Required: HuggingFace token for pyannote models
huggingface_token: "your_huggingface_token_here"

# Required for Gemini LLM (or set GEMINI_API_KEY environment variable)
gemini_api_key: "your_gemini_api_key_here"
```

### 3. Choose LLM Provider

```yaml
# Option 1: Google Gemini (cloud-based)
llm_provider: "gemini"
gemini_model: "gemini-1.5-flash"

# Option 2: Aya Expanse (local)
llm_provider: "aya-expanse"
aya_context_length: 4096
```

### 4. Adjust Performance Settings

```yaml
# GPU settings
device: "cuda"  # or "cpu"
granite_num_beams: 4     # Beam search size (must be > 1)
granite_max_tokens: 200  # Max tokens per transcription
llm_batch_size: 8        # Adjust based on LLM capacity
```

## 🎬 Usage

### Basic Usage

```bash
# Process single video file (enhancement only)
python autosub-withllm.py --input video.mp4 --output ./output --llm gemini

# Process directory of videos
python autosub-withllm.py --input ./videos/ --output ./output --llm aya-expanse

# Use custom configuration
python autosub-withllm.py --config my-config.yaml --input ./videos/ --output ./output
```

### Translation Mode

```bash
# Enhance and translate to English
python autosub-withllm.py --input video.mp4 --output ./output --llm gemini --translate "English"

# Enhance and translate to Vietnamese
python autosub-withllm.py --input video.mp4 --output ./output --llm gemini --translate "Vietnamese"

# Enhance and translate to French
python autosub-withllm.py --input ./videos/ --output ./output --llm aya-expanse --translate "French"

# Enhance and translate to German
python autosub-withllm.py --input video.mp4 --output ./output --llm gemini --translate "German"

# Enhance and translate to Chinese
python autosub-withllm.py --input video.mp4 --output ./output --llm gemini --translate "Chinese"
```

### Advanced Options

```bash
# Dry run to preview processing
python autosub-withllm.py --input ./videos/ --output ./output --dry-run

# Custom batch size and temp directory
python autosub-withllm.py --input video.mp4 --output ./output \
  --batch-size 16 --temp-dir /fast-storage/temp

# Keep intermediate files for debugging
python autosub-withllm.py --input video.mp4 --output ./output \
  --keep-intermediates --verbose
```

### Command Line Options

| Option | Description | Default |
|--------|-------------|---------|
| `--input, -i` | Input video file or directory | Required |
| `--output, -o` | Output directory | Required |
| `--config, -c` | Configuration file path | `config.yaml` |
| `--llm` | LLM provider (`gemini` or `aya-expanse`) | `gemini` |
| `--translate` | Target language for translation (e.g., 'English', 'Vietnamese') | None (enhancement only) |
| `--batch-size` | Batch size for LLM processing | `8` |
| `--temp-dir` | Custom temporary directory | System temp |
| `--dry-run` | Preview without processing | `False` |
| `--keep-intermediates` | Keep intermediate files | `False` |
| `--verbose, -v` | Enable verbose logging | `False` |

## 📁 Output Structure

For each processed video, the pipeline creates:

```
output/
├── video_name/
│   ├── 01_preprocessed/     # Converted audio files
│   ├── 02_diarization/      # RTTM diarization files
│   ├── 03_segments/         # Audio segments
│   ├── 05_denoised/         # Enhanced audio
│   ├── 06_transcripts/      # Granite Speech transcriptions
│   ├── 07_llm_enhanced/     # LLM-enhanced transcriptions
│   └── 08_srt_output/       # Final SRT files
│       ├── video_name.srt           # Combined subtitles
│       ├── video_name_speaker1.srt  # Speaker-specific
│       ├── video_name_speaker2.srt  # Speaker-specific
│       └── video_name_timing_report.json
└── autosub-withllm.log      # Processing log
```

## 🎛️ LLM Configuration

### Google Gemini Setup

1. Get API key from [Google AI Studio](https://makersuite.google.com/app/apikey)
2. Set in config or environment:

```bash
export GEMINI_API_KEY="your_api_key_here"
```

### Aya Expanse Setup

The local Aya Expanse model will be automatically downloaded on first use:

```yaml
aya_context_length: 4096  # Adjust based on available RAM
aya_threads: 4           # Adjust based on CPU cores
aya_max_tokens: 1024     # Maximum response length
aya_temperature: 0.3     # Lower = more deterministic
```

## 🔧 Troubleshooting

### Common Issues

1. **CUDA Out of Memory**
   ```yaml
   # Reduce batch sizes in config.yaml
   granite_num_beams: 2
   llm_batch_size: 4
   ```

2. **Gemini API Rate Limits**
   ```yaml
   # Increase rate limiting delay
   gemini_rate_limit: 2.0
   ```

3. **Missing Dependencies**
   ```bash
   # Install missing packages
   pip install transformers accelerate peft soundfile google-generativeai
   ```

4. **FFmpeg Not Found**
   ```bash
   # Install FFmpeg
   # Windows: Download from https://ffmpeg.org/
   # Linux: sudo apt install ffmpeg
   # macOS: brew install ffmpeg
   ```

### Performance Optimization

1. **GPU Memory**: Reduce `granite_num_beams` if getting OOM errors
2. **Processing Speed**: Increase batch sizes if you have more GPU memory
3. **Storage**: Use fast SSD for `temp_dir` to improve I/O performance
4. **CPU**: Increase `aya_threads` for local LLM processing

## 📊 Quality Settings

### Maximum Quality Configuration

```yaml
# High-quality denoising
denoising:
  cfm_steps: 128
  solver: "midpoint"
  use_fp16: false

# Optimal SRT formatting
srt_max_line_length: 42
srt_max_lines: 2
include_speaker_labels: true
```

### Fast Processing Configuration

```yaml
# Faster processing
denoising:
  cfm_steps: 64
  solver: "euler"
  use_fp16: true

# Larger batches
granite_num_beams: 8
llm_batch_size: 16
```

## 🤝 Contributing

Contributions are welcome! Please see the main project README for contribution guidelines.

## 📄 License

This project follows the same license as the main TTS data pipeline project.

## 🆘 Support

For issues and questions:
1. Check the troubleshooting section above
2. Review the processing logs in `autosub-withllm.log`
3. Use `--verbose` flag for detailed debugging information
4. Open an issue in the project repository
