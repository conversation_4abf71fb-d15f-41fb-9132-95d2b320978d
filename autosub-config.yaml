# AutoSub with LLM Configuration
# Configuration file for the AutoSub with LLM pipeline

# Authentication
huggingface_token: "*************************************"  # Required for pyannote models
gemini_api_key: "AIzaSyCUxjTqKUGiMcBXURXhoxq4zJXPZBqx1Kw"  # Required for Gemini LLM (or set GEMINI_API_KEY env var)

# Device and performance settings
device: "cuda"  # Use "cpu" if no GPU is available
autocast_dtype: "bf16"  # Options: "fp32", "bf16", "fp16", or null (to disable autocast)

# NVIDIA Canary Model Settings
canary_batch_size: 4  # Batch size for Canary transcription (adjust based on GPU memory)

# LLM Configuration
llm_provider: "gemini"  # Options: "gemini", "aya-expanse"
llm_batch_size: 32  # Number of transcriptions to process in each LLM batch

# Translation Configuration
translation_mode: true  # Enable translation mode (will be overridden by --translate argument)
translate_target: "Vietnamese"  # Target language for translation (e.g., "English", "Vietnamese", "French", "German", "Spanish", "Chinese", "Japanese")
llm_prompt_template: |
  ### prompt
  Please enhance and correct the following ASR (Automatic Speech Recognition) transcriptions for accuracy, grammar, and linguistic correctness.

  ### instructions
  The goal is to accurately enhance ASR transcriptions by fixing errors, improving punctuation, and ensuring linguistic correctness while preserving the original meaning and timing.

  You will receive a batch of transcription lines for enhancement. Carefully read through each line, understanding the context and language being spoken.
  Enhance each line accurately, concisely, and separately, with appropriate punctuation and grammar.

  The enhanced output must have the same number of lines as the original input, but you can correct errors and improve the content to fit proper grammar and linguistic rules.

  Make sure to enhance all provided lines and do not ask whether to continue.

  **CRITICAL LANGUAGE-SPECIFIC RULES:**

  **Vietnamese Language:**
  - Restore proper Vietnamese diacritics and tones: á, à, ả, ã, ạ, ă, ắ, ằ, ẳ, ẵ, ặ, â, ấ, ầ, ẩ, ẫ, ậ, é, è, ẻ, ẽ, ẹ, ê, ế, ề, ể, ễ, ệ, í, ì, ỉ, ĩ, ị, ó, ò, ỏ, õ, ọ, ô, ố, ồ, ổ, ỗ, ộ, ơ, ớ, ờ, ở, ỡ, ợ, ú, ù, ủ, ũ, ụ, ư, ứ, ừ, ử, ữ, ự, ý, ỳ, ỷ, ỹ, ỵ, đ
  - Fix Vietnamese consonant clusters: ng, nh, ph, th, tr, ch, kh, gh, qu, gi
  - Correct Vietnamese word boundaries and compound words
  - Preserve Vietnamese sentence structure and word order
  - Handle Vietnamese honorifics and formal/informal speech patterns

  **European Languages (French, German, Spanish, Italian, Portuguese, Russian):**
  - Restore proper accents and diacritics for each language
  - Fix language-specific grammar rules and conjugations
  - Correct articles, prepositions, and case systems
  - Handle formal/informal address appropriately
  - Preserve regional language variations

  **Asian Languages (Chinese, Japanese, Korean, Thai):**
  - Restore proper scripts (Hanzi, Hiragana/Katakana/Kanji, Hangul, Thai script)
  - Fix romanization errors back to native scripts
  - Handle tone systems and character recognition errors
  - Preserve honorific systems and cultural context
  - Correct script-specific spacing and punctuation rules

  **Universal Rules:**
  - Add proper punctuation (periods, commas, question marks, exclamation points)
  - Capitalize sentence beginnings and proper nouns
  - Fix obvious ASR errors using contextual understanding
  - Preserve speaker intent and emotional tone
  - Do not add content that wasn't spoken
  - Maintain the same semantic timing and meaning

  Your response will be processed by an automated system, so you MUST respond using the required format:

  {transcriptions}

# Gemini-specific settings
gemini_model: "gemini-2.0-flash-lite"  # Gemini model to use (options: gemini-1.5-flash, gemini-1.5-pro, gemini-1.0-pro)
gemini_rate_limit: 3.0  # Seconds to wait between API calls
gemini_temperature: 0.2  # Temperature for text generation (0.0-1.0, lower = more deterministic)
gemini_max_output_tokens: 4096  # Maximum tokens to generate
gemini_top_p: 0.8  # Top-p sampling parameter (0.0-1.0)
gemini_top_k: 40  # Top-k sampling parameter

# Aya Expanse (local LLM) settings
aya_context_length: 4096  # Context length for Aya Expanse
aya_threads: 4  # Number of CPU threads for Aya Expanse
aya_max_tokens: 1024  # Maximum tokens to generate
aya_temperature: 0.3  # Temperature for text generation (0.0-1.0)

# Denoising configuration (Resemble Enhance settings)
# MAXIMUM QUALITY SETTINGS - Configured for highest possible audio quality
denoising:
  sample_rate: 44100        # Sample rate for denoiser processing (44.1kHz recommended)
  chunk_seconds: 60.0       # Duration of each processing chunk (longer = better quality)
  overlap_seconds: 2.0      # Overlap between chunks (more overlap = smoother transitions)
  denoise_before_enhancement: true  # Whether to denoise before enhancement

  # CFM (Conditional Flow Matching) Settings - MAXIMUM QUALITY
  # These settings match the HuggingFace interface for maximum quality output
  cfm_steps: 128           # CFM function evaluations (64-512, MAXIMUM for best quality)
  cfm_temperature: 0.6     # CFM prior temperature (0.1-1.0, lower = more stable/deterministic)
  solver: "midpoint"              # CFM solver: "euler" (fastest), "midpoint", "rk4" (MAXIMUM quality)

  # Precision settings for maximum quality
  use_fp16: false          # Use FP32/FP64 for maximum precision (slower but highest quality)

# Audio cleaning configuration
cleaning:
  min_duration: 1.0        # Minimum audio duration in seconds (files shorter than this will be excluded)
  bandwidth_threshold: 5000 # Frequency threshold in Hz for noise detection (lower = more strict)

# SRT Generation Settings
srt_max_line_length: 42  # Maximum characters per line in SRT subtitles
srt_max_lines: 2  # Maximum lines per subtitle
include_speaker_labels: true  # Include speaker labels in subtitles
generate_comparison_srt: false  # Generate comparison SRT showing original vs enhanced

# Processing Settings
execution_mode: "step_sequential"  # Process all files through each step before moving to next step
skip_cleaning: true  # Skip the cleaning step (04_cleaned) for AutoSub pipeline
generate_srt: true  # Generate SRT files as final output
keep_intermediates: false  # Keep intermediate processing files for debugging

# File Extensions
video_extensions: ['.mp4', '.avi', '.mov', '.mkv', '.wmv', '.flv', '.webm']
audio_extensions: ['.wav', '.mp3', '.flac', '.m4a', '.ogg']

# Temporary Directory (optional)
# temp_dir: "D:/temp"  # Custom temporary directory location (uncomment to use)

# Model parameters (for compatibility with existing pipeline)
chunk_size: 64
left_context_size: 128
right_context_size: 128
total_batch_duration: 14400  # Maximum audio duration in seconds to process at once

# Output options
print_results: true
save_results: true

# HuggingFace dataset upload configuration (optional)
huggingface:
  dataset_name: "autosub-processed-dataset"  # Name for the dataset
  repo_id: "your_username/your-dataset-name"  # HuggingFace repository ID
  private: false  # Whether the dataset should be private
