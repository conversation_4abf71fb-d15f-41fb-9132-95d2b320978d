#!/usr/bin/env python3
"""
AutoSub with LLM - TTS Data Pipeline Variant for SRT Generation

This script implements a complete TTS data pipeline variant with LLM-enhanced 
transcription post-processing. The final output is SRT subtitle files that are 
accurately synchronized with the input videos.

Core Pipeline Structure:
- 01_preprocessed: Video-to-audio conversion and normalization
- 02_diarization: Speaker diarization and segmentation
- 03_segments: Audio segments from diarization
- 05_denoised: Enhanced audio quality (skips 04_cleaned)
- 06_transcripts: NVIDIA Canary transcriptions
- 07_llm_enhanced: LLM post-processed transcriptions
- 08_llm_translated: LLM translated transcriptions (optional)
- 09_srt_output: Final SRT subtitle files (08_srt_output if no translation)

Key Features:
- NVIDIA Canary model for high-quality ASR
- LLM post-processing (Google Gemini or local Aya Expanse)
- Batch processing with contextual understanding
- Precise video synchronization for SRT output
- Step-sequential processing with memory management

Usage:
    python autosub-withllm.py --input video.mp4 --output ./output --llm gemini
    python autosub-withllm.py --input ./videos/ --output ./output --llm aya-expanse
    python autosub-withllm.py --config config.yaml --input ./videos/ --output ./output
"""

import argparse
import sys
import shutil
import tempfile
import os
import time
from pathlib import Path
from typing import List, Dict, Any, Optional, Union
import yaml
import logging

# Setup logging
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(levelname)s - %(message)s',
    handlers=[
        logging.FileHandler('autosub-withllm.log'),
        logging.StreamHandler()
    ]
)
logger = logging.getLogger(__name__)

# Import pipeline modules
sys.path.insert(0, str(Path(__file__).parent))

try:
    from pipeline.preprocess import preprocess_audio
    PREPROCESS_AVAILABLE = True
except ImportError:
    logger.warning("⚠️  Preprocess module not available")
    PREPROCESS_AVAILABLE = False

try:
    from pipeline.diarization import perform_speaker_diarization
    DIARIZATION_AVAILABLE = True
    logger.info("✅ Diarization module loaded successfully")
except ImportError as e:
    logger.warning(f"⚠️  Diarization module not available: {e}")
    logger.warning("⚠️  This may be due to missing pyannote.audio or HuggingFace token")
    DIARIZATION_AVAILABLE = False

try:
    from pipeline.utils.file import parse_rttm, normalize_filename
    FILE_UTILS_AVAILABLE = True
except ImportError:
    logger.warning("⚠️  File utilities not available")
    FILE_UTILS_AVAILABLE = False

    def normalize_filename(filename: str, file_id: str) -> str:
        """Fallback function for normalize_filename."""
        import re
        # Simple filename normalization
        clean_name = re.sub(r'[^\w\-_\.]', '_', filename)
        return clean_name.lower()

    def parse_rttm(rttm_path: str):
        """Fallback function for parse_rttm."""
        logger.error("❌ RTTM parsing not available - install pipeline modules")
        return None

try:
    from pipeline.utils.audio import split_audio_segments
    AUDIO_UTILS_AVAILABLE = True
except ImportError:
    logger.warning("⚠️  Audio utilities not available")
    AUDIO_UTILS_AVAILABLE = False

    def split_audio_segments(rttm_path: str, audio_path: str, output_dir: str):
        """Fallback function for split_audio_segments."""
        logger.error("❌ Audio segmentation not available - install pipeline modules")
        return []

try:
    from pipeline.utils.model_manager import cleanup_between_steps, log_memory_usage
    MODEL_MANAGER_AVAILABLE = True
except ImportError:
    logger.warning("⚠️  Model manager not available")
    MODEL_MANAGER_AVAILABLE = False

    def cleanup_between_steps(step_name: str):
        """Fallback function for cleanup_between_steps."""
        import gc
        import torch
        gc.collect()
        if torch.cuda.is_available():
            torch.cuda.empty_cache()
        logger.info(f"🧹 Memory cleanup after {step_name}")

    def log_memory_usage(step_name: str):
        """Fallback function for log_memory_usage."""
        logger.debug(f"📊 Memory usage at {step_name}")

try:
    from pipeline.denoise import denoise_audio_pipeline
    DENOISE_AVAILABLE = True
    logger.info("✅ Denoise module loaded successfully")
except ImportError as e:
    logger.warning(f"⚠️  Denoise module not available: {e}")
    logger.warning("⚠️  Using fallback (copy without denoising)")
    DENOISE_AVAILABLE = False

    def denoise_audio_pipeline(input_path, output_dir, config):
        """Fallback function that copies files without denoising."""
        import shutil
        from pathlib import Path

        input_path = Path(input_path)
        output_dir = Path(output_dir)
        output_dir.mkdir(parents=True, exist_ok=True)

        if input_path.is_file():
            # Single file
            output_file = output_dir / input_path.name
            shutil.copy2(input_path, output_file)
            return {"status": "success", "processed_files": 1, "failed_files": 0}
        elif input_path.is_dir():
            # Directory
            audio_extensions = {'.wav', '.mp3', '.flac', '.m4a', '.aac', '.ogg'}
            audio_files = [f for f in input_path.rglob('*') if f.suffix.lower() in audio_extensions]

            for audio_file in audio_files:
                output_file = output_dir / audio_file.name
                shutil.copy2(audio_file, output_file)

            return {"status": "success", "processed_files": len(audio_files), "failed_files": 0}

        return {"status": "failed", "processed_files": 0, "failed_files": 0}

# Import new modules
try:
    from pipeline.transcribe_canary import transcribe_with_granite
    GRANITE_AVAILABLE = True
except ImportError:
    logger.warning("⚠️  Granite Speech transcription module not available")
    GRANITE_AVAILABLE = False

try:
    from pipeline.llm_postprocess import enhance_transcriptions_with_llm, translate_transcriptions_with_llm
    LLM_POSTPROCESS_AVAILABLE = True
except ImportError:
    logger.warning("⚠️  LLM post-processing module not available")
    LLM_POSTPROCESS_AVAILABLE = False

try:
    from pipeline.utils.srt_generator import generate_srt_files
    from pipeline.utils.timing_utils import synchronize_with_video
    SRT_AVAILABLE = True
except ImportError:
    logger.warning("⚠️  SRT generation modules not available")
    SRT_AVAILABLE = False


class AutoSubWithLLM:
    """Main class for AutoSub with LLM processing."""
    
    def __init__(self, config: Dict[str, Any]):
        """Initialize with configuration."""
        self.config = config
        self.temp_dir = None
        self.results = {
            "processed_files": 0,
            "failed_files": 0,
            "total_duration": 0,
            "processing_time": 0,
            "steps": {}
        }
        
    def __enter__(self):
        """Context manager entry."""
        # Create custom temp directory if specified
        temp_location = self.config.get('temp_dir', None)
        if temp_location:
            temp_location = Path(temp_location)
            temp_location.mkdir(parents=True, exist_ok=True)
            self.temp_dir = tempfile.mkdtemp(dir=temp_location)
        else:
            self.temp_dir = tempfile.mkdtemp()
        
        logger.info(f"🗂️  Using temporary directory: {self.temp_dir}")
        return self
        
    def __exit__(self, exc_type, exc_val, exc_tb):
        """Context manager exit with cleanup."""
        if self.temp_dir and Path(self.temp_dir).exists():
            try:
                shutil.rmtree(self.temp_dir)
                logger.info("🧹 Cleaned up temporary directory")
            except Exception as e:
                logger.warning(f"⚠️  Failed to clean up temp directory: {e}")


def parse_arguments() -> argparse.Namespace:
    """Parse command line arguments."""
    parser = argparse.ArgumentParser(
        description="AutoSub with LLM - Generate SRT subtitles with LLM-enhanced transcription",
        formatter_class=argparse.RawDescriptionHelpFormatter,
        epilog="""
Examples:
  # Process single video with Gemini LLM
  python autosub-withllm.py --input video.mp4 --output ./output --llm gemini
  
  # Process directory with local Aya Expanse LLM
  python autosub-withllm.py --input ./videos/ --output ./output --llm aya-expanse
  
  # Use configuration file
  python autosub-withllm.py --config config.yaml --input ./videos/ --output ./output
  
  # Dry run to preview processing
  python autosub-withllm.py --input ./videos/ --output ./output --dry-run
        """
    )
    
    parser.add_argument(
        "--input", "-i",
        type=str,
        required=True,
        help="Input video file or directory containing videos"
    )
    
    parser.add_argument(
        "--output", "-o", 
        type=str,
        required=True,
        help="Output directory for processed files and SRT subtitles"
    )
    
    parser.add_argument(
        "--config", "-c",
        type=str,
        default="config.yaml",
        help="Configuration file path (default: config.yaml)"
    )
    
    parser.add_argument(
        "--llm",
        choices=["gemini", "aya-expanse"],
        default="gemini",
        help="LLM to use for transcription enhancement (default: gemini)"
    )

    parser.add_argument(
        "--translate",
        type=str,
        help="Target language for translation (e.g., 'English', 'Vietnamese', 'French'). If specified, will translate instead of enhance transcriptions."
    )
    
    parser.add_argument(
        "--batch-size",
        type=int,
        default=8,
        help="Batch size for LLM processing (default: 8)"
    )
    
    parser.add_argument(
        "--temp-dir",
        type=str,
        help="Custom temporary directory location (default: system temp)"
    )
    
    parser.add_argument(
        "--dry-run",
        action="store_true",
        help="Preview what would be processed without actual processing"
    )
    
    parser.add_argument(
        "--keep-intermediates",
        action="store_true", 
        help="Keep intermediate processing files (for debugging)"
    )
    
    parser.add_argument(
        "--verbose", "-v",
        action="store_true",
        help="Enable verbose logging"
    )
    
    return parser.parse_args()


def load_configuration(config_path: str, args: argparse.Namespace) -> Dict[str, Any]:
    """Load and merge configuration from file and command line arguments."""
    try:
        # Load base configuration
        with open(config_path, 'r', encoding='utf-8') as f:
            config = yaml.safe_load(f)
            
        # Override with command line arguments
        config.update({
            "input_path": args.input,
            "output_dir": args.output,
            "llm_provider": args.llm,
            "llm_batch_size": args.batch_size,
            "temp_dir": args.temp_dir,
            "dry_run": args.dry_run,
            "keep_intermediates": args.keep_intermediates,
            "verbose": args.verbose,
            "translate_target": args.translate,
            "translation_mode": bool(args.translate)
        })
        
        # Add AutoSub-specific settings
        config.update({
            "skip_cleaning": True,  # Skip 04_cleaned step
            "generate_srt": True,   # Generate SRT files
            "execution_mode": "step_sequential",  # Process all files through each step
            "video_extensions": ('.mp4', '.avi', '.mov', '.mkv', '.wmv', '.flv', '.webm'),
            "audio_extensions": ('.wav', '.mp3', '.flac', '.m4a', '.ogg')
        })
        
        logger.info(f"✅ Configuration loaded from {config_path}")
        return config
        
    except FileNotFoundError:
        logger.error(f"❌ Configuration file not found: {config_path}")
        sys.exit(1)
    except yaml.YAMLError as e:
        logger.error(f"❌ Error parsing configuration file: {e}")
        sys.exit(1)


def validate_configuration(config: Dict[str, Any]) -> None:
    """Validate configuration parameters."""
    required_keys = [
        "input_path", "output_dir", "llm_provider", 
        "huggingface_token", "device"
    ]
    
    for key in required_keys:
        if key not in config:
            raise ValueError(f"Missing required configuration key: {key}")
    
    # Validate input path
    input_path = Path(config["input_path"])
    if not input_path.exists():
        raise ValueError(f"Input path does not exist: {input_path}")
    
    # Validate LLM provider
    if config["llm_provider"] not in ["gemini", "aya-expanse"]:
        raise ValueError(f"Invalid LLM provider: {config['llm_provider']}")
    
    # Check for LLM-specific requirements
    if config["llm_provider"] == "gemini":
        if not config.get("gemini_api_key"):
            logger.warning("⚠️  Gemini API key not found. Set GEMINI_API_KEY environment variable.")
    
    logger.info("✅ Configuration validation passed")


def discover_media_files(input_path: Path, config: Dict[str, Any]) -> List[Path]:
    """Discover video and audio files for processing."""
    media_files = []
    
    video_extensions = config.get("video_extensions", ('.mp4', '.avi', '.mov', '.mkv', '.wmv', '.flv', '.webm'))
    audio_extensions = config.get("audio_extensions", ('.wav', '.mp3', '.flac', '.m4a', '.ogg'))
    all_extensions = video_extensions + audio_extensions
    
    if input_path.is_file():
        if input_path.suffix.lower() in all_extensions:
            media_files.append(input_path)
        else:
            logger.warning(f"⚠️  Unsupported file format: {input_path}")
    
    elif input_path.is_dir():
        logger.info(f"🔍 Scanning directory: {input_path}")
        
        for ext in all_extensions:
            media_files.extend(input_path.glob(f"*{ext}"))
            media_files.extend(input_path.glob(f"*{ext.upper()}"))
        
        # Sort for consistent processing order
        media_files.sort()
    
    else:
        raise ValueError(f"Input path is neither file nor directory: {input_path}")
    
    logger.info(f"📊 Found {len(media_files)} media files")
    
    # Group by type for statistics
    video_files = [f for f in media_files if f.suffix.lower() in video_extensions]
    audio_files = [f for f in media_files if f.suffix.lower() in audio_extensions]
    
    if video_files:
        logger.info(f"   🎬 Video files: {len(video_files)}")
    if audio_files:
        logger.info(f"   🎵 Audio files: {len(audio_files)}")
    
    return media_files


def process_step_sequential_autosub(
    media_files: List[Path],
    output_dir: Path,
    config: Dict[str, Any],
    processor: AutoSubWithLLM
) -> Dict[str, Any]:
    """
    Process files using step-sequential approach for AutoSub pipeline.

    Steps:
    1. Preprocessing (all files)
    2. Diarization (all files)
    3. Denoising (all files) - skip cleaning step
    4. NVIDIA Canary Transcription (all files)
    5. LLM Enhancement (all files)
    6. SRT Generation (all files)
    """

    # Initialize file states for tracking
    file_states = {}
    for media_file in media_files:
        file_id = normalize_filename(media_file.name, None)
        file_output_dir = output_dir / media_file.stem

        file_states[media_file.stem] = {
            "input_path": media_file,
            "output_dir": file_output_dir,
            "file_id": file_id,
            "status": "pending",
            "current_files": [],
            "steps": {},
            "output_files": {},
            "timing_info": {}
        }

        # Create output directory
        file_output_dir.mkdir(parents=True, exist_ok=True)

    batch_results = {
        "total_files": len(media_files),
        "processed": 0,
        "failed": 0,
        "steps": {},
        "processing_time": 0
    }

    start_time = time.time()

    # Step 1: Preprocessing (all files)
    logger.info(f"\n{'='*60}")
    logger.info("📋 STEP 1: Preprocessing all files...")
    logger.info(f"{'='*60}")
    log_memory_usage("Step 1 start")

    process_step_preprocessing_autosub(file_states, config)
    cleanup_between_steps("Step 1: Preprocessing")

    # Step 2: Diarization (all files)
    logger.info(f"\n{'='*60}")
    logger.info("🎤 STEP 2: Diarization for all files...")
    logger.info(f"{'='*60}")
    log_memory_usage("Step 2 start")

    process_step_diarization_autosub(file_states, config)
    cleanup_between_steps("Step 2: Diarization")

    # Step 3: Denoising (all files) - Skip cleaning step
    logger.info(f"\n{'='*60}")
    logger.info("🔊 STEP 3: Denoising all files...")
    logger.info(f"{'='*60}")
    log_memory_usage("Step 3 start")

    process_step_denoising_autosub(file_states, config)
    cleanup_between_steps("Step 3: Denoising")

    # Step 4: NVIDIA Canary Transcription (all files)
    logger.info(f"\n{'='*60}")
    logger.info("🎙️  STEP 4: IBM Granite Speech Transcription for all files...")
    logger.info(f"{'='*60}")
    log_memory_usage("Step 4 start")

    process_step_granite_transcription(file_states, config)
    cleanup_between_steps("Step 4: Granite Speech Transcription")

    # Step 5: LLM Enhancement (all files)
    logger.info(f"\n{'='*60}")
    logger.info("🤖 STEP 5: LLM Enhancement for all files...")
    logger.info(f"{'='*60}")
    log_memory_usage("Step 5 start")

    process_step_llm_enhancement(file_states, config)
    cleanup_between_steps("Step 5: LLM Enhancement")

    # Step 6: LLM Translation (all files) - Optional
    if config.get("translation_mode", False):
        logger.info(f"\n{'='*60}")
        logger.info(f"🌍 STEP 6: LLM Translation to {config.get('translate_target', 'target language')} for all files...")
        logger.info(f"{'='*60}")
        log_memory_usage("Step 6 start")

        process_step_llm_translation(file_states, config)
        cleanup_between_steps("Step 6: LLM Translation")

    # Step 7: SRT Generation (all files)
    logger.info(f"\n{'='*60}")
    step_num = 7 if config.get("translation_mode", False) else 6
    logger.info(f"📝 STEP {step_num}: SRT Generation for all files...")
    logger.info(f"{'='*60}")
    log_memory_usage(f"Step {step_num} start")

    process_step_srt_generation(file_states, config)
    cleanup_between_steps(f"Step {step_num}: SRT Generation")

    # Calculate final results
    end_time = time.time()
    batch_results["processing_time"] = end_time - start_time

    # Count successful and failed files
    for file_state in file_states.values():
        if file_state["status"] == "completed":
            batch_results["processed"] += 1
        else:
            batch_results["failed"] += 1

    # Update processor results
    processor.results.update(batch_results)

    return batch_results


def process_step_preprocessing_autosub(file_states: Dict, config: Dict[str, Any]) -> None:
    """Process preprocessing step for all files in AutoSub pipeline."""
    results = {"processed": 0, "failed": 0}

    for file_state in file_states.values():
        if file_state["status"] == "failed":
            continue

        try:
            logger.info(f"📋 Preprocessing: {file_state['input_path'].name}")

            # Create preprocessing output directory
            preprocess_dir = file_state["output_dir"] / "01_preprocessed"
            preprocess_dir.mkdir(parents=True, exist_ok=True)

            # Preprocess the file (handles video-to-audio conversion)
            preprocessed_path = preprocess_audio(
                file_state["input_path"],
                preprocess_dir,
                file_state["file_id"]
            )

            if preprocessed_path:
                file_state["steps"]["preprocess"] = {"status": "success", "output": str(preprocessed_path)}
                file_state["output_files"]["preprocessed"] = str(preprocessed_path)
                file_state["current_files"] = [preprocessed_path]
                results["processed"] += 1
                logger.info(f"✅ Preprocessing completed: {file_state['input_path'].name}")
            else:
                file_state["status"] = "failed"
                file_state["steps"]["preprocess"] = {"status": "failed", "error": "Preprocessing failed"}
                results["failed"] += 1
                logger.error(f"❌ Preprocessing failed: {file_state['input_path'].name}")

        except Exception as e:
            file_state["status"] = "failed"
            file_state["steps"]["preprocess"] = {"status": "failed", "error": str(e)}
            results["failed"] += 1
            logger.error(f"❌ Preprocessing error for {file_state['input_path'].name}: {str(e)}")

    logger.info(f"📋 Preprocessing completed: {results['processed']} successful, {results['failed']} failed")


def process_step_diarization_autosub(file_states: Dict, config: Dict[str, Any]) -> None:
    """Process diarization step for all files in AutoSub pipeline."""
    results = {"processed": 0, "failed": 0}

    for file_state in file_states.values():
        if file_state["status"] == "failed" or not file_state["current_files"]:
            continue

        try:
            current_audio = file_state["current_files"][0]  # Should be preprocessed audio
            logger.info(f"🎤 Diarization: {current_audio.name}")

            # Create diarization output directories
            diarization_dir = file_state["output_dir"] / "02_diarization"
            segments_dir = file_state["output_dir"] / "03_segments"
            diarization_dir.mkdir(parents=True, exist_ok=True)
            segments_dir.mkdir(parents=True, exist_ok=True)

            # Perform diarization
            rttm_path = diarization_dir / f"{current_audio.stem}.rttm"
            perform_speaker_diarization(str(current_audio), str(rttm_path), config_path="config.yaml")

            # Parse RTTM file to get segment information
            segments_df = parse_rttm(str(rttm_path))

            if segments_df is not None and len(segments_df) > 0:
                # Split audio into segments using parsed RTTM data
                split_audio_segments(str(rttm_path), str(current_audio), str(segments_dir))

                file_state["steps"]["diarization"] = {
                    "status": "success",
                    "rttm": str(rttm_path),
                    "segments_count": len(segments_df)
                }
                file_state["output_files"]["rttm"] = str(rttm_path)
                file_state["output_files"]["segments_dir"] = str(segments_dir)

                # Store timing information for SRT generation
                file_state["timing_info"]["segments"] = segments_df.to_dict('records')

                # Use segments for further processing
                segment_files = list(segments_dir.glob("*.wav"))
                if segment_files:
                    logger.info(f"✅ Created {len(segment_files)} audio segments from {len(segments_df)} RTTM entries")
                    file_state["current_files"] = segment_files
                else:
                    logger.warning("⚠️ No segments created, using original file")
                    file_state["current_files"] = [current_audio]
            else:
                logger.warning("⚠️ No valid segments found in RTTM file, using original file")
                file_state["steps"]["diarization"] = {"status": "warning", "message": "No segments found"}
                file_state["current_files"] = [current_audio]

            results["processed"] += 1
            logger.info(f"✅ Diarization completed: {current_audio.name}")

        except Exception as e:
            file_state["status"] = "failed"
            file_state["steps"]["diarization"] = {"status": "failed", "error": str(e)}
            results["failed"] += 1
            logger.error(f"❌ Diarization error for {file_state['input_path'].name}: {str(e)}")

    logger.info(f"🎤 Diarization completed: {results['processed']} successful, {results['failed']} failed")


def process_step_denoising_autosub(file_states: Dict, config: Dict[str, Any]) -> None:
    """Process denoising step for all files in AutoSub pipeline."""
    results = {"processed": 0, "failed": 0}

    for file_state in file_states.values():
        if file_state["status"] == "failed" or not file_state["current_files"]:
            continue

        try:
            logger.info(f"🔊 Denoising: {file_state['input_path'].name}")

            # Create denoising output directory
            denoised_dir = file_state["output_dir"] / "05_denoised"
            denoised_dir.mkdir(parents=True, exist_ok=True)

            # Denoise all current files (segments)
            if DENOISE_AVAILABLE:
                # Create temporary directory for batch processing
                import tempfile
                import shutil

                with tempfile.TemporaryDirectory() as temp_dir:
                    temp_path = Path(temp_dir)

                    # Copy all files to temp directory
                    for audio_file in file_state["current_files"]:
                        if audio_file.exists():
                            shutil.copy2(audio_file, temp_path / audio_file.name)

                    # Run batch denoising
                    result = denoise_audio_pipeline(
                        input_path=temp_path,
                        output_dir=denoised_dir,
                        config=config
                    )

                    # Collect denoised files
                    denoised_files = []
                    if result.get("status") == "success":
                        # Find denoised files in output directory
                        for audio_file in file_state["current_files"]:
                            denoised_name = f"denoised_{audio_file.name}"
                            denoised_path = denoised_dir / denoised_name
                            if denoised_path.exists():
                                denoised_files.append(denoised_path)
                            else:
                                # Fallback: copy original file if denoising failed
                                fallback_path = denoised_dir / audio_file.name
                                shutil.copy2(audio_file, fallback_path)
                                denoised_files.append(fallback_path)
                    else:
                        # If denoising failed, copy original files
                        for audio_file in file_state["current_files"]:
                            fallback_path = denoised_dir / audio_file.name
                            shutil.copy2(audio_file, fallback_path)
                            denoised_files.append(fallback_path)
            else:
                # Fallback: copy files without denoising
                import shutil
                denoised_files = []
                for audio_file in file_state["current_files"]:
                    output_file = denoised_dir / audio_file.name
                    shutil.copy2(audio_file, output_file)
                    denoised_files.append(output_file)
                logger.info(f"📋 Fallback: Copied {len(denoised_files)} files (no denoising applied)")

            if denoised_files:
                file_state["steps"]["denoise"] = {
                    "status": "success",
                    "denoised_count": len(denoised_files)
                }
                file_state["output_files"]["denoised_dir"] = str(denoised_dir)
                file_state["current_files"] = denoised_files
                results["processed"] += 1
                logger.info(f"✅ Denoising completed: {len(denoised_files)} files processed")
            else:
                file_state["status"] = "failed"
                file_state["steps"]["denoise"] = {"status": "failed", "error": "No files denoised"}
                results["failed"] += 1
                logger.error(f"❌ Denoising failed: {file_state['input_path'].name}")

        except Exception as e:
            file_state["status"] = "failed"
            file_state["steps"]["denoise"] = {"status": "failed", "error": str(e)}
            results["failed"] += 1
            logger.error(f"❌ Denoising error for {file_state['input_path'].name}: {str(e)}")

    logger.info(f"🔊 Denoising completed: {results['processed']} successful, {results['failed']} failed")


def process_step_granite_transcription(file_states: Dict, config: Dict[str, Any]) -> None:
    """Process IBM Granite Speech transcription step for all files."""
    results = {"processed": 0, "failed": 0}

    for file_state in file_states.values():
        if file_state["status"] == "failed" or not file_state["current_files"]:
            continue

        try:
            logger.info(f"🎙️  Granite Speech Transcription: {file_state['input_path'].name}")

            # Create transcription output directory
            transcripts_dir = file_state["output_dir"] / "06_transcripts"
            transcripts_dir.mkdir(parents=True, exist_ok=True)

            # Transcribe with IBM Granite Speech
            transcription_results = transcribe_with_granite(
                file_state["current_files"],
                transcripts_dir,
                config
            )

            if transcription_results:
                file_state["steps"]["granite_transcription"] = {
                    "status": "success",
                    "transcriptions_count": len(transcription_results)
                }
                file_state["output_files"]["transcripts_dir"] = str(transcripts_dir)
                file_state["output_files"]["transcription_results"] = transcription_results
                results["processed"] += 1
                logger.info(f"✅ Granite Speech transcription completed: {len(transcription_results)} transcriptions")
            else:
                file_state["status"] = "failed"
                file_state["steps"]["granite_transcription"] = {"status": "failed", "error": "No transcriptions generated"}
                results["failed"] += 1
                logger.error(f"❌ Granite Speech transcription failed: {file_state['input_path'].name}")

        except Exception as e:
            file_state["status"] = "failed"
            file_state["steps"]["granite_transcription"] = {"status": "failed", "error": str(e)}
            results["failed"] += 1
            logger.error(f"❌ Granite Speech transcription error for {file_state['input_path'].name}: {str(e)}")

    logger.info(f"🎙️  Granite Speech transcription completed: {results['processed']} successful, {results['failed']} failed")


def process_step_llm_enhancement(file_states: Dict, config: Dict[str, Any]) -> None:
    """Process LLM enhancement step for all files."""
    results = {"processed": 0, "failed": 0}

    for file_state in file_states.values():
        if file_state["status"] == "failed" or "transcription_results" not in file_state["output_files"]:
            continue

        try:
            logger.info(f"🤖 LLM Enhancement: {file_state['input_path'].name}")

            # Create LLM enhancement output directory
            llm_dir = file_state["output_dir"] / "07_llm_enhanced"
            llm_dir.mkdir(parents=True, exist_ok=True)

            # Enhance transcriptions with LLM
            enhanced_transcriptions = enhance_transcriptions_with_llm(
                file_state["output_files"]["transcription_results"],
                llm_dir,
                config
            )

            if enhanced_transcriptions:
                file_state["steps"]["llm_enhancement"] = {
                    "status": "success",
                    "enhanced_count": len(enhanced_transcriptions)
                }
                file_state["output_files"]["llm_dir"] = str(llm_dir)
                file_state["output_files"]["enhanced_transcriptions"] = enhanced_transcriptions
                results["processed"] += 1
                logger.info(f"✅ LLM enhancement completed: {len(enhanced_transcriptions)} enhanced transcriptions")
            else:
                file_state["status"] = "failed"
                file_state["steps"]["llm_enhancement"] = {"status": "failed", "error": "No enhanced transcriptions generated"}
                results["failed"] += 1
                logger.error(f"❌ LLM enhancement failed: {file_state['input_path'].name}")

        except Exception as e:
            file_state["status"] = "failed"
            file_state["steps"]["llm_enhancement"] = {"status": "failed", "error": str(e)}
            results["failed"] += 1
            logger.error(f"❌ LLM enhancement error for {file_state['input_path'].name}: {str(e)}")

    logger.info(f"🤖 LLM enhancement completed: {results['processed']} successful, {results['failed']} failed")


def process_step_llm_translation(file_states: Dict, config: Dict[str, Any]) -> None:
    """Process LLM translation step for all files."""
    results = {"processed": 0, "failed": 0}

    for file_state in file_states.values():
        if file_state["status"] == "failed" or "enhanced_transcriptions" not in file_state["output_files"]:
            continue

        try:
            logger.info(f"🌍 LLM Translation: {file_state['input_path'].name}")

            # Create translation output directory
            translation_dir = file_state["output_dir"] / "07_llm_translated"
            translation_dir.mkdir(parents=True, exist_ok=True)

            # Translate enhanced transcriptions with LLM
            translated_transcriptions = translate_transcriptions_with_llm(
                file_state["output_files"]["enhanced_transcriptions"],
                translation_dir,
                config
            )

            if translated_transcriptions:
                file_state["steps"]["llm_translation"] = {
                    "status": "success",
                    "translated_count": len(translated_transcriptions)
                }
                file_state["output_files"]["translation_dir"] = str(translation_dir)
                file_state["output_files"]["translated_transcriptions"] = translated_transcriptions
                results["processed"] += 1
                logger.info(f"✅ LLM translation completed: {len(translated_transcriptions)} translated transcriptions")
            else:
                file_state["status"] = "failed"
                file_state["steps"]["llm_translation"] = {"status": "failed", "error": "No translated transcriptions generated"}
                results["failed"] += 1
                logger.error(f"❌ LLM translation failed: {file_state['input_path'].name}")

        except Exception as e:
            file_state["status"] = "failed"
            file_state["steps"]["llm_translation"] = {"status": "failed", "error": str(e)}
            results["failed"] += 1
            logger.error(f"❌ LLM translation error for {file_state['input_path'].name}: {str(e)}")

    logger.info(f"🌍 LLM translation completed: {results['processed']} successful, {results['failed']} failed")


def process_step_srt_generation(file_states: Dict, config: Dict[str, Any]) -> None:
    """Process SRT generation step for all files."""
    results = {"processed": 0, "failed": 0}

    for file_state in file_states.values():
        # Check for translated transcriptions first, then enhanced, then skip
        transcriptions_to_use = None
        transcription_type = ""

        if "translated_transcriptions" in file_state["output_files"]:
            transcriptions_to_use = file_state["output_files"]["translated_transcriptions"]
            transcription_type = "translated"
        elif "enhanced_transcriptions" in file_state["output_files"]:
            transcriptions_to_use = file_state["output_files"]["enhanced_transcriptions"]
            transcription_type = "enhanced"

        if file_state["status"] == "failed" or not transcriptions_to_use:
            continue

        try:
            logger.info(f"📝 SRT Generation ({transcription_type}): {file_state['input_path'].name}")

            # Create SRT output directory
            step_num = "08" if not config.get("translation_mode", False) else "09"
            srt_dir = file_state["output_dir"] / f"{step_num}_srt_output"
            srt_dir.mkdir(parents=True, exist_ok=True)

            # Generate SRT files with video synchronization
            srt_files = generate_srt_files(
                transcriptions_to_use,
                file_state["timing_info"],
                file_state["input_path"],  # Original video file for synchronization
                srt_dir,
                config
            )

            if srt_files:
                file_state["steps"]["srt_generation"] = {
                    "status": "success",
                    "srt_files": [str(f) for f in srt_files]
                }
                file_state["output_files"]["srt_dir"] = str(srt_dir)
                file_state["output_files"]["srt_files"] = [str(f) for f in srt_files]
                file_state["status"] = "completed"  # Mark as fully completed
                results["processed"] += 1
                logger.info(f"✅ SRT generation completed: {len(srt_files)} SRT files created")
            else:
                file_state["status"] = "failed"
                file_state["steps"]["srt_generation"] = {"status": "failed", "error": "No SRT files generated"}
                results["failed"] += 1
                logger.error(f"❌ SRT generation failed: {file_state['input_path'].name}")

        except Exception as e:
            file_state["status"] = "failed"
            file_state["steps"]["srt_generation"] = {"status": "failed", "error": str(e)}
            results["failed"] += 1
            logger.error(f"❌ SRT generation error for {file_state['input_path'].name}: {str(e)}")

    logger.info(f"📝 SRT generation completed: {results['processed']} successful, {results['failed']} failed")


if __name__ == "__main__":
    # Parse arguments and load configuration
    args = parse_arguments()

    # Set verbose logging if requested
    if args.verbose:
        logging.getLogger().setLevel(logging.DEBUG)
        logger.debug("🔍 Verbose logging enabled")

    try:
        # Load and validate configuration
        config = load_configuration(args.config, args)
        validate_configuration(config)

        # Discover media files
        input_path = Path(config["input_path"])
        media_files = discover_media_files(input_path, config)

        if not media_files:
            logger.error("❌ No media files found to process")
            sys.exit(1)

        # Show dry run information
        if config["dry_run"]:
            logger.info("🔍 DRY RUN MODE - No actual processing will be performed")
            logger.info(f"   📁 Input: {input_path}")
            logger.info(f"   📁 Output: {config['output_dir']}")
            logger.info(f"   🤖 LLM: {config['llm_provider']}")
            logger.info(f"   📊 Files to process: {len(media_files)}")

            for i, media_file in enumerate(media_files[:5], 1):
                logger.info(f"     {i}. {media_file.name}")
            if len(media_files) > 5:
                logger.info(f"     ... and {len(media_files) - 5} more files")

            logger.info("🔍 DRY RUN completed")
            sys.exit(0)

        # Start processing
        start_time = time.time()
        logger.info("🚀 Starting AutoSub with LLM processing...")
        logger.info(f"   📁 Input: {input_path}")
        logger.info(f"   📁 Output: {config['output_dir']}")
        logger.info(f"   🤖 LLM Provider: {config['llm_provider']}")
        logger.info(f"   📊 Files to process: {len(media_files)}")

        # Create output directory
        output_dir = Path(config["output_dir"])
        output_dir.mkdir(parents=True, exist_ok=True)

        with AutoSubWithLLM(config) as processor:
            # Process files using step-sequential approach
            results = process_step_sequential_autosub(
                media_files,
                output_dir,
                config,
                processor
            )

            # Display final results
            end_time = time.time()
            total_time = end_time - start_time

            logger.info(f"\n{'='*60}")
            logger.info("📊 FINAL RESULTS")
            logger.info(f"{'='*60}")
            logger.info(f"   📁 Total files processed: {results['total_files']}")
            logger.info(f"   ✅ Successfully completed: {results['processed']}")
            logger.info(f"   ❌ Failed: {results['failed']}")
            logger.info(f"   ⏱️  Total processing time: {total_time:.2f} seconds")

            if results['processed'] > 0:
                avg_time = total_time / results['processed']
                logger.info(f"   📈 Average time per file: {avg_time:.2f} seconds")

            # Show SRT file locations
            if results['processed'] > 0:
                logger.info(f"\n📝 SRT files generated in:")
                logger.info(f"   📁 {output_dir}")
                logger.info(f"   📂 Look for 08_srt_output directories in each processed file folder")

            # Cleanup intermediate files if not keeping them
            if not config.get("keep_intermediates", False):
                logger.info("\n🧹 Cleaning up intermediate files...")
                # TODO: Implement cleanup logic
                logger.info("   ✅ Cleanup completed")

        if results['failed'] > 0:
            logger.warning(f"⚠️  {results['failed']} files failed processing. Check logs for details.")
            sys.exit(1)
        else:
            logger.info("✅ All files processed successfully!")

    except KeyboardInterrupt:
        logger.info("⏹️  Processing interrupted by user")
        sys.exit(1)
    except Exception as e:
        logger.error(f"❌ Error during processing: {str(e)}")
        if args.verbose:
            import traceback
            logger.error(traceback.format_exc())
        sys.exit(1)
