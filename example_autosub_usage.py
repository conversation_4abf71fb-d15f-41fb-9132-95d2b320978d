#!/usr/bin/env python3
"""
AutoSub with LLM Usage Examples

This script demonstrates various ways to use the AutoSub with LLM pipeline
for generating high-quality SRT subtitle files from video content.

Examples include:
- Single video processing
- Batch video processing
- Different LLM configurations
- Custom settings and options
"""

import os
import sys
import subprocess
from pathlib import Path
import logging

# Setup logging
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(levelname)s - %(message)s')
logger = logging.getLogger(__name__)


def run_autosub_command(cmd_args, description):
    """Run AutoSub command and handle results."""
    logger.info(f"🚀 {description}")
    logger.info(f"Command: python autosub-withllm.py {' '.join(cmd_args)}")
    
    try:
        # Build full command
        cmd = [sys.executable, "autosub-withllm.py"] + cmd_args
        
        # Run command
        result = subprocess.run(cmd, capture_output=True, text=True, timeout=3600)  # 1 hour timeout
        
        if result.returncode == 0:
            logger.info("✅ Command completed successfully")
            if result.stdout:
                logger.info("Output:")
                for line in result.stdout.split('\n')[-10:]:  # Show last 10 lines
                    if line.strip():
                        logger.info(f"  {line}")
        else:
            logger.error("❌ Command failed")
            if result.stderr:
                logger.error("Error output:")
                for line in result.stderr.split('\n')[-5:]:  # Show last 5 error lines
                    if line.strip():
                        logger.error(f"  {line}")
        
        return result.returncode == 0
        
    except subprocess.TimeoutExpired:
        logger.error("❌ Command timed out after 1 hour")
        return False
    except Exception as e:
        logger.error(f"❌ Command execution failed: {str(e)}")
        return False


def example_single_video_gemini():
    """Example: Process single video with Gemini LLM."""
    logger.info("\n" + "="*60)
    logger.info("EXAMPLE 1: Single Video with Gemini LLM")
    logger.info("="*60)
    
    # Check if example video exists
    video_file = "example_video.mp4"
    if not Path(video_file).exists():
        logger.warning(f"⚠️  Example video not found: {video_file}")
        logger.info("   Create or download a sample video file to test this example")
        return False
    
    cmd_args = [
        "--input", video_file,
        "--output", "./output_gemini",
        "--llm", "gemini",
        "--config", "autosub-config.yaml"
    ]
    
    return run_autosub_command(cmd_args, "Processing single video with Gemini LLM")


def example_single_video_aya():
    """Example: Process single video with Aya Expanse LLM."""
    logger.info("\n" + "="*60)
    logger.info("EXAMPLE 2: Single Video with Aya Expanse LLM")
    logger.info("="*60)
    
    video_file = "example_video.mp4"
    if not Path(video_file).exists():
        logger.warning(f"⚠️  Example video not found: {video_file}")
        return False
    
    cmd_args = [
        "--input", video_file,
        "--output", "./output_aya",
        "--llm", "aya-expanse",
        "--config", "autosub-config.yaml"
    ]
    
    return run_autosub_command(cmd_args, "Processing single video with Aya Expanse LLM")


def example_batch_processing():
    """Example: Batch process multiple videos."""
    logger.info("\n" + "="*60)
    logger.info("EXAMPLE 3: Batch Processing Multiple Videos")
    logger.info("="*60)
    
    # Check if example directory exists
    video_dir = "./example_videos"
    if not Path(video_dir).exists():
        logger.warning(f"⚠️  Example video directory not found: {video_dir}")
        logger.info("   Create a directory with sample video files to test this example")
        return False
    
    cmd_args = [
        "--input", video_dir,
        "--output", "./output_batch",
        "--llm", "gemini",
        "--batch-size", "4",
        "--config", "autosub-config.yaml"
    ]
    
    return run_autosub_command(cmd_args, "Batch processing multiple videos")


def example_dry_run():
    """Example: Dry run to preview processing."""
    logger.info("\n" + "="*60)
    logger.info("EXAMPLE 4: Dry Run Preview")
    logger.info("="*60)
    
    video_dir = "./example_videos"
    if not Path(video_dir).exists():
        # Create a dummy directory for dry run demo
        Path(video_dir).mkdir(exist_ok=True)
        logger.info(f"Created example directory: {video_dir}")
    
    cmd_args = [
        "--input", video_dir,
        "--output", "./output_preview",
        "--llm", "gemini",
        "--dry-run",
        "--config", "autosub-config.yaml"
    ]
    
    return run_autosub_command(cmd_args, "Dry run preview of processing")


def example_custom_settings():
    """Example: Custom settings and options."""
    logger.info("\n" + "="*60)
    logger.info("EXAMPLE 5: Custom Settings and Options")
    logger.info("="*60)
    
    video_file = "example_video.mp4"
    if not Path(video_file).exists():
        logger.warning(f"⚠️  Example video not found: {video_file}")
        return False
    
    cmd_args = [
        "--input", video_file,
        "--output", "./output_custom",
        "--llm", "gemini",
        "--batch-size", "16",
        "--temp-dir", "./custom_temp",
        "--keep-intermediates",
        "--verbose",
        "--config", "autosub-config.yaml"
    ]
    
    return run_autosub_command(cmd_args, "Processing with custom settings")


def example_test_setup():
    """Example: Test setup and configuration."""
    logger.info("\n" + "="*60)
    logger.info("EXAMPLE 6: Test Setup and Configuration")
    logger.info("="*60)
    
    cmd_args = ["--config", "autosub-config.yaml"]
    
    try:
        cmd = [sys.executable, "test_autosub_setup.py"] + cmd_args
        result = subprocess.run(cmd, capture_output=True, text=True)
        
        logger.info("Setup test output:")
        for line in result.stdout.split('\n'):
            if line.strip():
                logger.info(f"  {line}")
        
        if result.stderr:
            logger.warning("Setup test warnings/errors:")
            for line in result.stderr.split('\n'):
                if line.strip():
                    logger.warning(f"  {line}")
        
        return result.returncode == 0
        
    except Exception as e:
        logger.error(f"❌ Setup test failed: {str(e)}")
        return False


def create_sample_config():
    """Create a sample configuration file if it doesn't exist."""
    config_file = Path("autosub-config.yaml")
    
    if config_file.exists():
        logger.info(f"✅ Configuration file already exists: {config_file}")
        return True
    
    try:
        # Copy from template
        template_file = Path("autosub-config.yaml")
        if template_file.exists():
            logger.info(f"✅ Using existing configuration template: {template_file}")
            return True
        
        logger.warning("⚠️  Configuration file not found")
        logger.info("   Please copy autosub-config.yaml and configure it with your settings")
        return False
        
    except Exception as e:
        logger.error(f"❌ Failed to create configuration: {str(e)}")
        return False


def main():
    """Main example runner."""
    logger.info("🎬 AutoSub with LLM Usage Examples")
    logger.info("="*60)
    
    # Check if we're in the right directory
    if not Path("autosub-withllm.py").exists():
        logger.error("❌ autosub-withllm.py not found in current directory")
        logger.info("   Please run this script from the TTS-data-pipeline directory")
        return 1
    
    # Check configuration
    if not create_sample_config():
        logger.error("❌ Configuration setup failed")
        return 1
    
    # Run examples
    examples = [
        ("Setup Test", example_test_setup),
        ("Dry Run Preview", example_dry_run),
        ("Single Video (Gemini)", example_single_video_gemini),
        ("Single Video (Aya Expanse)", example_single_video_aya),
        ("Batch Processing", example_batch_processing),
        ("Custom Settings", example_custom_settings)
    ]
    
    results = []
    
    for example_name, example_func in examples:
        logger.info(f"\n🔄 Running: {example_name}")
        try:
            result = example_func()
            results.append((example_name, result))
            
            if result:
                logger.info(f"✅ {example_name} completed successfully")
            else:
                logger.warning(f"⚠️  {example_name} failed or skipped")
                
        except Exception as e:
            logger.error(f"❌ {example_name} crashed: {str(e)}")
            results.append((example_name, False))
    
    # Summary
    logger.info("\n" + "="*60)
    logger.info("📊 EXAMPLES SUMMARY")
    logger.info("="*60)
    
    successful = 0
    for example_name, result in results:
        status = "✅ SUCCESS" if result else "❌ FAILED/SKIPPED"
        logger.info(f"{status} - {example_name}")
        if result:
            successful += 1
    
    logger.info(f"\nResults: {successful}/{len(results)} examples completed successfully")
    
    if successful > 0:
        logger.info("🎉 Some examples completed! Check the output directories for results.")
    else:
        logger.warning("⚠️  No examples completed successfully. Check configuration and setup.")
    
    return 0


if __name__ == "__main__":
    sys.exit(main())
