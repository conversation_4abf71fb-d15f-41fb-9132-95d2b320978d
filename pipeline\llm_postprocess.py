"""
LLM Post-Processing Module for AutoSub with LLM

This module provides LLM-enhanced transcription post-processing using either
Google Gemini or local Aya Expanse models. The LLMs process batches of raw 
ASR transcriptions to improve accuracy and maintain contextual coherence.

Features:
- Dual LLM support (Google Gemini / Local Aya Expanse)
- Batch processing with contextual understanding
- Configurable prompts and parameters
- Error correction and punctuation improvement
- Speaker consistency maintenance
"""

import os
import sys
import json
import logging
from pathlib import Path
from typing import List, Dict, Any, Optional, Union
import tempfile
from tqdm import tqdm
import time

# Setup logging
logger = logging.getLogger(__name__)

# Import LLM libraries with fallbacks
try:
    import google.generativeai as genai
    GEMINI_AVAILABLE = True
except ImportError:
    GEMINI_AVAILABLE = False
    logger.warning("⚠️  Google Generative AI not available. Install with: pip install google-generativeai")

try:
    from llama_cpp import Llama
    LLAMA_CPP_AVAILABLE = True
except ImportError:
    LLAMA_CPP_AVAILABLE = False
    logger.warning("⚠️  llama-cpp-python not available. Install with: pip install llama-cpp-python")


class LLMPostProcessor:
    """Base class for LLM post-processing."""
    
    def __init__(self, config: Dict[str, Any]):
        """Initialize LLM post-processor with configuration."""
        self.config = config
        self.batch_size = config.get("llm_batch_size", 8)
        self.provider = config.get("llm_provider", "gemini")
        
    def enhance_transcriptions(self, transcriptions: List[Dict[str, Any]]) -> List[Dict[str, Any]]:
        """Enhance transcriptions using LLM. To be implemented by subclasses."""
        raise NotImplementedError
    
    def _create_enhancement_prompt(self, transcription_batch: List[str]) -> str:
        """Create prompt for LLM enhancement."""
        prompt_template = self.config.get("llm_prompt_template", """
### prompt
Please enhance and correct the following ASR (Automatic Speech Recognition) transcriptions for accuracy, grammar, and linguistic correctness.

### instructions
The goal is to accurately enhance ASR transcriptions by fixing errors, improving punctuation, and ensuring linguistic correctness while preserving the original meaning and timing.

You will receive a batch of transcription lines for enhancement. Carefully read through each line, understanding the context and language being spoken.
Enhance each line accurately, concisely, and separately, with appropriate punctuation and grammar.

The enhanced output must have the same number of lines as the original input, but you can correct errors and improve the content to fit proper grammar and linguistic rules.

Make sure to enhance all provided lines and do not ask whether to continue.

**CRITICAL LANGUAGE-SPECIFIC RULES:**

**Vietnamese Language:**
- Restore proper Vietnamese diacritics and tones: á, à, ả, ã, ạ, ă, ắ, ằ, ẳ, ẵ, ặ, â, ấ, ầ, ẩ, ẫ, ậ, é, è, ẻ, ẽ, ẹ, ê, ế, ề, ể, ễ, ệ, í, ì, ỉ, ĩ, ị, ó, ò, ỏ, õ, ọ, ô, ố, ồ, ổ, ỗ, ộ, ơ, ớ, ờ, ở, ỡ, ợ, ú, ù, ủ, ũ, ụ, ư, ứ, ừ, ử, ữ, ự, ý, ỳ, ỷ, ỹ, ỵ, đ
- Fix Vietnamese consonant clusters: ng, nh, ph, th, tr, ch, kh, gh, qu, gi
- Correct Vietnamese word boundaries and compound words
- Preserve Vietnamese sentence structure and word order
- Handle Vietnamese honorifics and formal/informal speech patterns

**Chinese (Mandarin/Cantonese):**
- Preserve traditional and simplified Chinese characters correctly
- Handle tone markers and pinyin romanization errors
- Fix character recognition errors common in ASR
- Maintain proper Chinese grammar and sentence structure
- Handle measure words (量词) correctly

**Japanese Language:**
- Restore proper hiragana, katakana, and kanji characters
- Fix romanization errors (romaji to proper Japanese script)
- Handle particle usage correctly (は, が, を, に, で, etc.)
- Preserve keigo (honorific language) levels
- Correct katakana for foreign loanwords

**Korean Language:**
- Restore proper Hangul characters and syllable blocks
- Fix romanization errors (especially ㅓ/ㅗ, ㅡ/ㅜ confusion)
- Handle Korean honorifics and speech levels correctly
- Preserve Korean word spacing rules
- Fix consonant cluster errors (ㄱㅅ, ㄴㅈ, etc.)

**Thai Language:**
- Restore proper Thai script and tone markers
- Fix romanization back to Thai script
- Handle Thai consonant clusters and vowel combinations
- Preserve Thai sentence structure (no spaces between words)
- Correct tone-related character confusions

**European Languages:**

**French Language:**
- Restore proper French accents: é, è, ê, ë, à, â, ä, ç, î, ï, ô, ö, ù, û, ü, ÿ
- Fix liaison and elision errors
- Correct French contractions (du, des, au, aux)
- Handle French gender agreement
- Preserve formal/informal address (tu/vous)

**German Language:**
- Restore German umlauts: ä, ö, ü, ß
- Fix German compound word separation/joining
- Correct German case system (Nominativ, Akkusativ, Dativ, Genitiv)
- Handle German verb positioning
- Preserve German capitalization rules for nouns

**Spanish Language:**
- Restore Spanish accents: á, é, í, ó, ú, ñ, ü
- Fix Spanish gender agreement (el/la, un/una)
- Correct Spanish verb conjugations
- Handle Spanish question/exclamation marks (¿¡)
- Preserve regional variations (Latin American vs. Iberian)

**Italian Language:**
- Restore Italian accents: à, è, é, ì, í, î, ò, ó, ù, ú
- Fix Italian double consonants
- Correct Italian articles and prepositions
- Handle Italian verb conjugations
- Preserve Italian regional expressions

**Portuguese Language:**
- Restore Portuguese accents: á, â, ã, à, é, ê, í, ó, ô, õ, ú, ç
- Fix Portuguese nasal sounds (ão, ões, ãe)
- Correct Portuguese contractions
- Handle Brazilian vs. European Portuguese differences
- Preserve Portuguese verb conjugations

**Russian Language:**
- Restore proper Cyrillic characters
- Fix romanization errors back to Cyrillic
- Handle Russian case system correctly
- Preserve Russian aspect pairs (perfective/imperfective)
- Correct Russian soft/hard consonant distinctions

**English Language:**
- Fix common homophones: there/their/they're, to/too/two, your/you're, its/it's
- Correct contractions and apostrophes
- Fix capitalization for proper nouns, sentence beginnings
- Handle technical terms and acronyms correctly

**Mixed Language Content:**
- Preserve code-switching patterns naturally
- Maintain technical terms in their original language when appropriate
- Handle proper nouns and brand names correctly
- Respect cultural context when mixing languages

**Universal Rules:**
- Add proper punctuation (periods, commas, question marks, exclamation points)
- Capitalize sentence beginnings and proper nouns
- Fix obvious ASR errors using contextual understanding
- Preserve speaker intent and emotional tone
- Do not add content that wasn't spoken
- Maintain the same semantic timing and meaning

**Examples:**

Vietnamese Enhancement:
#001
Original> toi den truong hoc hom nay
Enhanced> Tôi đến trường học hôm nay.

#002
Original> cam on ban rat nhieu vi da giup do toi
Enhanced> Cảm ơn bạn rất nhiều vì đã giúp đỡ tôi.

#003
Original> chung ta se lam viec cung nhau de hoan thanh du an nay
Enhanced> Chúng ta sẽ làm việc cùng nhau để hoàn thành dự án này.

Chinese Enhancement:
#004
Original> wo men jin tian qu xue xiao
Enhanced> 我们今天去学校。

#005
Original> xie xie ni de bang zhu
Enhanced> 谢谢你的帮助。

Japanese Enhancement:
#006
Original> watashi wa gakkou ni ikimasu
Enhanced> 私は学校に行きます。

#007
Original> arigatou gozaimasu
Enhanced> ありがとうございます。

Korean Enhancement:
#008
Original> annyeong haseyo oneul nal ssi ga joh ne yo
Enhanced> 안녕하세요. 오늘 날씨가 좋네요.

#009
Original> gam sa ham ni da
Enhanced> 감사합니다.

Thai Enhancement:
#010
Original> sa wat dee krap wan nee a gaat dee maak
Enhanced> สวัดดีครับ วันนี้อากาศดีมาก

French Enhancement:
#011
Original> je vais a lecole aujourdhui
Enhanced> Je vais à l'école aujourd'hui.

#012
Original> merci beaucoup pour votre aide
Enhanced> Merci beaucoup pour votre aide.

German Enhancement:
#013
Original> ich gehe heute zur schule
Enhanced> Ich gehe heute zur Schule.

#014
Original> vielen dank fur ihre hilfe
Enhanced> Vielen Dank für Ihre Hilfe.

Spanish Enhancement:
#015
Original> voy a la escuela hoy
Enhanced> Voy a la escuela hoy.

#016
Original> muchas gracias por su ayuda
Enhanced> Muchas gracias por su ayuda.

Italian Enhancement:
#017
Original> vado a scuola oggi
Enhanced> Vado a scuola oggi.

#018
Original> grazie mille per il vostro aiuto
Enhanced> Grazie mille per il vostro aiuto.

Portuguese Enhancement:
#019
Original> eu vou para a escola hoje
Enhanced> Eu vou para a escola hoje.

#020
Original> muito obrigado pela sua ajuda
Enhanced> Muito obrigado pela sua ajuda.

Russian Enhancement:
#021
Original> ya idu v shkolu segodnya
Enhanced> Я иду в школу сегодня.

#022
Original> spasibo bolshoye za vashu pomoshch
Enhanced> Спасибо большое за вашу помощь.

English Enhancement:
#023
Original> there going to the store to buy some groceries
Enhanced> They're going to the store to buy some groceries.

#024
Original> i cant here you can you speak louder
Enhanced> I can't hear you. Can you speak louder?

Mixed Language Enhancement:
#025
Original> toi dang lam viec voi artificial intelligence va machine learning
Enhanced> Tôi đang làm việc với artificial intelligence và machine learning.

#026
Original> meeting se bat dau luc hai gio chieu
Enhanced> Meeting sẽ bắt đầu lúc hai giờ chiều.

#027
Original> nous utilisons machine learning pour analyser les donnees
Enhanced> Nous utilisons machine learning pour analyser les données.

Technical Terms:
#028
Original> covid nineteen pandemic da anh huong den kinh te toan cau
Enhanced> COVID-19 pandemic đã ảnh hưởng đến kinh tế toàn cầu.

#029
Original> artificial intelligence est tres important pour lavenir
Enhanced> Artificial intelligence est très important pour l'avenir.

#030
Original> blockchain technology wird die zukunft verandern
Enhanced> Blockchain technology wird die Zukunft verändern.

Your response will be processed by an automated system, so you MUST respond using the required format:

{transcriptions}

### retry_instructions
There was an issue with the previous enhancement.

Please enhance the transcriptions again, ensuring each line is enhanced SEPARATELY, and EVERY line has a corresponding enhanced version.

Do NOT merge lines together in the enhancement, as this leads to incorrect timings and confusion for subtitle generation.
""")
        
        # Format transcriptions for the prompt in the required format
        formatted_transcriptions = "\n".join([
            f"#{i+1:03d}\nOriginal> {trans}\nEnhanced>" for i, trans in enumerate(transcription_batch)
        ])

        return prompt_template.format(transcriptions=formatted_transcriptions)


class GeminiPostProcessor(LLMPostProcessor):
    """Google Gemini LLM post-processor."""
    
    def __init__(self, config: Dict[str, Any]):
        """Initialize Gemini post-processor."""
        super().__init__(config)
        self.model = None
        self._setup_gemini()
    
    def _setup_gemini(self) -> bool:
        """Setup Gemini API."""
        if not GEMINI_AVAILABLE:
            logger.error("❌ Google Generative AI not available")
            return False
        
        try:
            # Get API key from config or environment
            api_key = self.config.get("gemini_api_key") or os.getenv("GEMINI_API_KEY")
            if not api_key:
                logger.error("❌ Gemini API key not found. Set GEMINI_API_KEY environment variable.")
                return False
            
            # Configure Gemini
            genai.configure(api_key=api_key)
            
            # Initialize model with custom configuration
            model_name = self.config.get("gemini_model", "gemini-1.5-flash")

            # Support for custom model configurations
            generation_config = {}
            if self.config.get("gemini_temperature"):
                generation_config["temperature"] = self.config.get("gemini_temperature")
            if self.config.get("gemini_max_output_tokens"):
                generation_config["max_output_tokens"] = self.config.get("gemini_max_output_tokens")
            if self.config.get("gemini_top_p"):
                generation_config["top_p"] = self.config.get("gemini_top_p")
            if self.config.get("gemini_top_k"):
                generation_config["top_k"] = self.config.get("gemini_top_k")

            # Initialize model with custom config if provided
            if generation_config:
                self.model = genai.GenerativeModel(
                    model_name=model_name,
                    generation_config=generation_config
                )
                logger.info(f"✅ Gemini model initialized: {model_name} with custom config: {generation_config}")
            else:
                self.model = genai.GenerativeModel(model_name)
                logger.info(f"✅ Gemini model initialized: {model_name}")

            return True
            
        except Exception as e:
            logger.error(f"❌ Failed to setup Gemini: {str(e)}")
            return False
    
    def enhance_transcriptions(self, transcriptions: List[Dict[str, Any]]) -> List[Dict[str, Any]]:
        """Enhance transcriptions using Gemini."""
        if not self.model:
            logger.error("❌ Gemini model not initialized")
            return transcriptions
        
        enhanced_results = []
        successful_transcriptions = [t for t in transcriptions if t.get("success", False)]
        
        if not successful_transcriptions:
            logger.warning("⚠️  No successful transcriptions to enhance")
            return transcriptions
        
        logger.info(f"🤖 Enhancing {len(successful_transcriptions)} transcriptions with Gemini...")
        
        try:
            # Process in batches
            for i in tqdm(range(0, len(successful_transcriptions), self.batch_size), desc="LLM Enhancement"):
                batch = successful_transcriptions[i:i + self.batch_size]
                batch_texts = [t["transcription"] for t in batch]
                
                try:
                    # Create prompt
                    prompt = self._create_enhancement_prompt(batch_texts)
                    
                    # Generate enhanced transcriptions
                    response = self.model.generate_content(prompt)
                    enhanced_texts = self._parse_gemini_response(response.text, len(batch_texts))
                    
                    # Update results
                    for j, (original, enhanced_text) in enumerate(zip(batch, enhanced_texts)):
                        enhanced_result = original.copy()
                        enhanced_result["original_transcription"] = original["transcription"]
                        enhanced_result["transcription"] = enhanced_text
                        enhanced_result["enhanced_by"] = "gemini"
                        enhanced_results.append(enhanced_result)
                    
                    # Rate limiting
                    time.sleep(self.config.get("gemini_rate_limit", 1.0))
                    
                except Exception as e:
                    logger.error(f"❌ Gemini batch enhancement failed: {str(e)}")
                    # Keep original transcriptions for failed batch
                    for original in batch:
                        enhanced_result = original.copy()
                        enhanced_result["enhanced_by"] = "failed"
                        enhanced_result["enhancement_error"] = str(e)
                        enhanced_results.append(enhanced_result)
            
            # Add back failed transcriptions
            for t in transcriptions:
                if not t.get("success", False):
                    enhanced_results.append(t)
            
            successful_enhanced = sum(1 for r in enhanced_results if r.get("enhanced_by") == "gemini")
            logger.info(f"✅ Gemini enhancement completed: {successful_enhanced}/{len(successful_transcriptions)} enhanced")
            
            return enhanced_results
            
        except Exception as e:
            logger.error(f"❌ Gemini enhancement error: {str(e)}")
            return transcriptions
    
    def _parse_gemini_response(self, response_text: str, expected_count: int) -> List[str]:
        """Parse Gemini response to extract enhanced transcriptions."""
        try:
            lines = response_text.strip().split('\n')
            enhanced_texts = []
            current_enhanced = None

            for line in lines:
                line = line.strip()

                # Look for "Enhanced>" lines which contain our enhanced text
                if line.startswith('Enhanced>'):
                    enhanced_text = line[9:].strip()  # Remove "Enhanced>" prefix
                    if enhanced_text:  # Only add non-empty enhanced text
                        enhanced_texts.append(enhanced_text)
                # Also handle direct numbered responses as fallback
                elif line and not line.startswith('#') and not line.startswith('Original>') and not line.startswith('Enhanced'):
                    # Remove numbering if present
                    if '. ' in line and line.split('. ', 1)[0].isdigit():
                        line = line.split('. ', 1)[1]
                    if line:  # Only add non-empty lines
                        enhanced_texts.append(line)

            # If we didn't get enough results, try alternative parsing
            if len(enhanced_texts) < expected_count:
                logger.warning(f"⚠️  Expected {expected_count} enhanced texts, got {len(enhanced_texts)}. Trying alternative parsing...")

                # Try to extract any meaningful text lines
                alternative_texts = []
                for line in lines:
                    line = line.strip()
                    if (line and
                        not line.startswith('#') and
                        not line.startswith('Original>') and
                        not line.startswith('Enhanced') and
                        not line.startswith('### ') and
                        len(line) > 3):  # Minimum meaningful length
                        alternative_texts.append(line)

                # Use alternative texts if we got more results
                if len(alternative_texts) >= len(enhanced_texts):
                    enhanced_texts = alternative_texts

            # Ensure we have the expected number of results
            while len(enhanced_texts) < expected_count:
                enhanced_texts.append("")  # Add empty strings for missing results

            return enhanced_texts[:expected_count]

        except Exception as e:
            logger.error(f"❌ Failed to parse Gemini response: {str(e)}")
            return [""] * expected_count


class AyaExpansePostProcessor(LLMPostProcessor):
    """Local Aya Expanse LLM post-processor."""
    
    def __init__(self, config: Dict[str, Any]):
        """Initialize Aya Expanse post-processor."""
        super().__init__(config)
        self.model = None
        self._setup_aya_expanse()
    
    def _setup_aya_expanse(self) -> bool:
        """Setup Aya Expanse model."""
        if not LLAMA_CPP_AVAILABLE:
            logger.error("❌ llama-cpp-python not available")
            return False
        
        try:
            logger.info("🔄 Loading Aya Expanse model...")
            
            # Model configuration
            model_config = {
                "repo_id": "lmstudio-community/aya-expanse-8b-GGUF",
                "filename": "aya-expanse-8b-Q4_K_M.gguf",
                "n_ctx": self.config.get("aya_context_length", 4096),
                "n_threads": self.config.get("aya_threads", 4),
                "verbose": False
            }
            
            # Load model
            self.model = Llama.from_pretrained(**model_config)
            
            logger.info("✅ Aya Expanse model loaded successfully")
            return True
            
        except Exception as e:
            logger.error(f"❌ Failed to setup Aya Expanse: {str(e)}")
            return False
    
    def enhance_transcriptions(self, transcriptions: List[Dict[str, Any]]) -> List[Dict[str, Any]]:
        """Enhance transcriptions using Aya Expanse."""
        if not self.model:
            logger.error("❌ Aya Expanse model not initialized")
            return transcriptions
        
        enhanced_results = []
        successful_transcriptions = [t for t in transcriptions if t.get("success", False)]
        
        if not successful_transcriptions:
            logger.warning("⚠️  No successful transcriptions to enhance")
            return transcriptions
        
        logger.info(f"🤖 Enhancing {len(successful_transcriptions)} transcriptions with Aya Expanse...")
        
        try:
            # Process in batches
            for i in tqdm(range(0, len(successful_transcriptions), self.batch_size), desc="LLM Enhancement"):
                batch = successful_transcriptions[i:i + self.batch_size]
                batch_texts = [t["transcription"] for t in batch]
                
                try:
                    # Create prompt
                    prompt = self._create_enhancement_prompt(batch_texts)
                    
                    # Generate enhanced transcriptions
                    response = self.model.create_chat_completion(
                        messages=[
                            {
                                "role": "user",
                                "content": prompt
                            }
                        ],
                        max_tokens=self.config.get("aya_max_tokens", 1024),
                        temperature=self.config.get("aya_temperature", 0.3)
                    )
                    
                    response_text = response['choices'][0]['message']['content']
                    enhanced_texts = self._parse_aya_response(response_text, len(batch_texts))
                    
                    # Update results
                    for j, (original, enhanced_text) in enumerate(zip(batch, enhanced_texts)):
                        enhanced_result = original.copy()
                        enhanced_result["original_transcription"] = original["transcription"]
                        enhanced_result["transcription"] = enhanced_text
                        enhanced_result["enhanced_by"] = "aya-expanse"
                        enhanced_results.append(enhanced_result)
                    
                except Exception as e:
                    logger.error(f"❌ Aya Expanse batch enhancement failed: {str(e)}")
                    # Keep original transcriptions for failed batch
                    for original in batch:
                        enhanced_result = original.copy()
                        enhanced_result["enhanced_by"] = "failed"
                        enhanced_result["enhancement_error"] = str(e)
                        enhanced_results.append(enhanced_result)
            
            # Add back failed transcriptions
            for t in transcriptions:
                if not t.get("success", False):
                    enhanced_results.append(t)
            
            successful_enhanced = sum(1 for r in enhanced_results if r.get("enhanced_by") == "aya-expanse")
            logger.info(f"✅ Aya Expanse enhancement completed: {successful_enhanced}/{len(successful_transcriptions)} enhanced")
            
            return enhanced_results
            
        except Exception as e:
            logger.error(f"❌ Aya Expanse enhancement error: {str(e)}")
            return transcriptions
    
    def _parse_aya_response(self, response_text: str, expected_count: int) -> List[str]:
        """Parse Aya Expanse response to extract enhanced transcriptions."""
        try:
            lines = response_text.strip().split('\n')
            enhanced_texts = []

            for line in lines:
                line = line.strip()

                # Look for "Enhanced>" lines which contain our enhanced text
                if line.startswith('Enhanced>'):
                    enhanced_text = line[9:].strip()  # Remove "Enhanced>" prefix
                    if enhanced_text:  # Only add non-empty enhanced text
                        enhanced_texts.append(enhanced_text)
                # Also handle direct numbered responses as fallback
                elif line and not line.startswith('#') and not line.startswith('Original>') and not line.startswith('Enhanced'):
                    # Remove numbering if present
                    if '. ' in line and line.split('. ', 1)[0].isdigit():
                        line = line.split('. ', 1)[1]
                    if line:  # Only add non-empty lines
                        enhanced_texts.append(line)

            # If we didn't get enough results, try alternative parsing
            if len(enhanced_texts) < expected_count:
                logger.warning(f"⚠️  Expected {expected_count} enhanced texts, got {len(enhanced_texts)}. Trying alternative parsing...")

                # Try to extract any meaningful text lines
                alternative_texts = []
                for line in lines:
                    line = line.strip()
                    if (line and
                        not line.startswith('#') and
                        not line.startswith('Original>') and
                        not line.startswith('Enhanced') and
                        not line.startswith('### ') and
                        len(line) > 3):  # Minimum meaningful length
                        alternative_texts.append(line)

                # Use alternative texts if we got more results
                if len(alternative_texts) >= len(enhanced_texts):
                    enhanced_texts = alternative_texts

            # Ensure we have the expected number of results
            while len(enhanced_texts) < expected_count:
                enhanced_texts.append("")  # Add empty strings for missing results

            return enhanced_texts[:expected_count]

        except Exception as e:
            logger.error(f"❌ Failed to parse Aya Expanse response: {str(e)}")
            return [""] * expected_count


def create_translation_prompt(transcription_batch: List[str], target_language: str) -> str:
    """Create prompt for LLM translation based on the subtitle translation format."""
    prompt_template = f"""
### prompt
Please translate the following subtitles to {target_language}.

### instructions
The goal is to accurately translate subtitles into {target_language}.

You will receive a batch of lines for translation. Carefully read through the lines, along with any additional context provided.
Translate each line accurately, concisely, and separately into {target_language}, with appropriate punctuation.

The translation must have the same number of lines as the original, but you can adapt the content to fit the grammar of {target_language}.

Make sure to translate all provided lines and do not ask whether to continue.

Use any provided context to enhance your translations. If a name list is provided, ensure names are spelled according to the user's preference.

If you detect obvious errors in the input, correct them in the translation using the available context, but do not improvise.

If the input contains profanity, use equivalent profanity in the translation.

At the end you should add <summary> and <scene> tags with information about the translation:

<summary>A one or two line synopsis of the current batch.</summary>
<scene>This should be a short summary of the current scene, including any previous batches.</scene>

If the context is unclear, just summarize the dialogue.

Your response will be processed by an automated system, so you MUST respond using the required format:

Example (translating to English):

#200
Original>
変わりゆく時代において、
Translation>
In an ever-changing era,

#501
Original>
進化し続けることが生き残る秘訣です。
Translation>
continuing to evolve is the key to survival.

Example (translating to German):

#700
Original>
In the age of digital transformation,
Translation>
Im Zeitalter der digitalen Transformation,

#701
Original>
those who resist change may find themselves left behind.
Translation>
diejenigen, die sich dem Wandel widersetzen,
könnten sich zurückgelassen finden.

Example (translating to Vietnamese):

#200
Original>
We're going to the market, aren't we
Translation>
Chúng ta sẽ đi chợ, phải không?

Example (translating to French, adapting an idiom):

#100
Original>
When life gives you lemons,
Translation>
Quand la vie vous donne des citrons,

#101
Original>
make lemonade.
Translation>
faites de la limonade.

{{transcriptions}}

### retry_instructions
There was an issue with the previous translation.

Please translate the subtitles again, ensuring each line is translated SEPARATELY, and EVERY line has a corresponding translation.

Do NOT merge lines together in the translation, as this leads to incorrect timings and confusion for the reader.
"""

    # Format transcriptions for the prompt
    formatted_transcriptions = "\n".join([
        f"#{i+1:03d}\nOriginal> {trans}\nTranslation>" for i, trans in enumerate(transcription_batch)
    ])

    return prompt_template.format(transcriptions=formatted_transcriptions)


def enhance_transcriptions_with_llm(
    transcriptions: List[Dict[str, Any]],
    output_dir: Path,
    config: Dict[str, Any]
) -> List[Dict[str, Any]]:
    """
    Enhance transcriptions using the configured LLM.
    
    Args:
        transcriptions: List of transcription results from Canary
        output_dir: Directory to save enhanced results
        config: Configuration dictionary
        
    Returns:
        List of enhanced transcription results
    """
    if not transcriptions:
        logger.warning("⚠️  No transcriptions provided for enhancement")
        return []
    
    provider = config.get("llm_provider", "gemini")
    logger.info(f"🤖 Starting LLM enhancement with {provider}")
    
    # Initialize appropriate processor
    if provider == "gemini":
        processor = GeminiPostProcessor(config)
    elif provider == "aya-expanse":
        processor = AyaExpansePostProcessor(config)
    else:
        logger.error(f"❌ Unknown LLM provider: {provider}")
        return transcriptions
    
    try:
        # Enhance transcriptions
        enhanced_results = processor.enhance_transcriptions(transcriptions)
        
        # Save enhanced results
        results_file = output_dir / "llm_enhanced_transcriptions.json"
        with open(results_file, 'w', encoding='utf-8') as f:
            json.dump(enhanced_results, f, indent=2, ensure_ascii=False)
        
        logger.info(f"💾 Enhanced transcriptions saved to: {results_file}")
        
        return enhanced_results
        
    except Exception as e:
        logger.error(f"❌ LLM enhancement failed: {str(e)}")
        return transcriptions


def translate_transcriptions_with_llm(
    transcriptions: List[Dict[str, Any]],
    output_dir: Path,
    config: Dict[str, Any]
) -> List[Dict[str, Any]]:
    """
    Translate transcriptions using the configured LLM.

    Args:
        transcriptions: List of enhanced transcription results
        output_dir: Directory to save translated results
        config: Configuration dictionary

    Returns:
        List of translated transcription results
    """
    if not transcriptions:
        logger.warning("⚠️  No transcriptions provided for translation")
        return []

    target_language = config.get("translate_target", "English")
    provider = config.get("llm_provider", "gemini")
    logger.info(f"🌍 Starting LLM translation to {target_language} with {provider}")

    # Initialize appropriate processor
    if provider == "gemini":
        processor = GeminiTranslationProcessor(config)
    elif provider == "aya-expanse":
        processor = AyaExpanseTranslationProcessor(config)
    else:
        logger.error(f"❌ Unknown LLM provider: {provider}")
        return transcriptions

    try:
        # Translate transcriptions
        translated_results = processor.translate_transcriptions(transcriptions, target_language)

        # Save translated results
        results_file = output_dir / "llm_translated_transcriptions.json"
        with open(results_file, 'w', encoding='utf-8') as f:
            json.dump(translated_results, f, indent=2, ensure_ascii=False)

        logger.info(f"💾 Translated transcriptions saved to: {results_file}")

        return translated_results

    except Exception as e:
        logger.error(f"❌ LLM translation failed: {str(e)}")
        return transcriptions


class GeminiTranslationProcessor(GeminiPostProcessor):
    """Gemini processor specialized for translation."""

    def translate_transcriptions(self, transcriptions: List[Dict[str, Any]], target_language: str) -> List[Dict[str, Any]]:
        """Translate transcriptions using Gemini."""
        if not self.model:
            logger.error("❌ Gemini model not initialized")
            return transcriptions

        translated_results = []
        successful_transcriptions = [t for t in transcriptions if t.get("success", False)]

        if not successful_transcriptions:
            logger.warning("⚠️  No successful transcriptions to translate")
            return transcriptions

        logger.info(f"🌍 Translating {len(successful_transcriptions)} transcriptions to {target_language} with Gemini...")

        try:
            # Process in batches
            for i in tqdm(range(0, len(successful_transcriptions), self.batch_size), desc="LLM Translation"):
                batch = successful_transcriptions[i:i + self.batch_size]
                batch_texts = [t["transcription"] for t in batch]

                try:
                    # Create translation prompt
                    prompt = create_translation_prompt(batch_texts, target_language)

                    # Generate translations
                    response = self.model.generate_content(prompt)
                    translated_texts = self._parse_translation_response(response.text, len(batch_texts))

                    # Update results
                    for j, (original, translated_text) in enumerate(zip(batch, translated_texts)):
                        translated_result = original.copy()
                        translated_result["original_transcription"] = original["transcription"]
                        translated_result["transcription"] = translated_text
                        translated_result["translated_by"] = "gemini"
                        translated_result["target_language"] = target_language
                        translated_results.append(translated_result)

                    # Rate limiting
                    time.sleep(self.config.get("gemini_rate_limit", 1.0))

                except Exception as e:
                    logger.error(f"❌ Gemini batch translation failed: {str(e)}")
                    # Keep original transcriptions for failed batch
                    for original in batch:
                        translated_result = original.copy()
                        translated_result["translated_by"] = "failed"
                        translated_result["translation_error"] = str(e)
                        translated_results.append(translated_result)

            # Add back failed transcriptions
            for t in transcriptions:
                if not t.get("success", False):
                    translated_results.append(t)

            successful_translated = sum(1 for r in translated_results if r.get("translated_by") == "gemini")
            logger.info(f"✅ Gemini translation completed: {successful_translated}/{len(successful_transcriptions)} translated")

            return translated_results

        except Exception as e:
            logger.error(f"❌ Gemini translation error: {str(e)}")
            return transcriptions

    def _parse_translation_response(self, response_text: str, expected_count: int) -> List[str]:
        """Parse Gemini translation response."""
        try:
            lines = response_text.strip().split('\n')
            translated_texts = []

            for line in lines:
                line = line.strip()

                # Look for "Translation>" lines which contain our translated text
                if line.startswith('Translation>'):
                    translated_text = line[12:].strip()  # Remove "Translation>" prefix
                    if translated_text:  # Only add non-empty translated text
                        translated_texts.append(translated_text)

            # Ensure we have the expected number of results
            while len(translated_texts) < expected_count:
                translated_texts.append("")  # Add empty strings for missing results

            return translated_texts[:expected_count]

        except Exception as e:
            logger.error(f"❌ Failed to parse Gemini translation response: {str(e)}")
            return [""] * expected_count


class AyaExpanseTranslationProcessor(AyaExpansePostProcessor):
    """Aya Expanse processor specialized for translation."""

    def translate_transcriptions(self, transcriptions: List[Dict[str, Any]], target_language: str) -> List[Dict[str, Any]]:
        """Translate transcriptions using Aya Expanse."""
        if not self.model:
            logger.error("❌ Aya Expanse model not initialized")
            return transcriptions

        # Similar implementation to Gemini but using Aya Expanse
        # For brevity, using the same structure as GeminiTranslationProcessor
        logger.info(f"🌍 Translating with Aya Expanse to {target_language}...")

        # Implementation would be similar to GeminiTranslationProcessor
        # but using self.model.create_chat_completion instead
        return transcriptions  # Placeholder for now


if __name__ == "__main__":
    # Test LLM availability
    logger.info("🧪 Testing LLM availability...")

    if GEMINI_AVAILABLE:
        logger.info("✅ Google Generative AI available")
    else:
        logger.warning("⚠️  Google Generative AI not available")

    if LLAMA_CPP_AVAILABLE:
        logger.info("✅ llama-cpp-python available")
    else:
        logger.warning("⚠️  llama-cpp-python not available")
