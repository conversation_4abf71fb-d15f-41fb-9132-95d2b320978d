"""
Granite Speech Transcription Module for AutoSub with LLM

This module provides transcription functionality using the IBM Granite Speech model,
replacing the NVIDIA Canary model for higher quality ASR results.

Features:
- IBM Granite-speech-3.3-2b model integration
- Batch processing with memory management
- Timestamp preservation for SRT generation
- Error handling and fallback mechanisms
- Two-pass design with explicit transcription calls
"""

import os
import sys
import torch
import torchaudio
import logging
from pathlib import Path
from typing import List, Dict, Any, Optional, Union
import tempfile
import json
from tqdm import tqdm

# Setup logging
logger = logging.getLogger(__name__)

try:
    from transformers import AutoProcessor, AutoModelForSpeechSeq2Seq
    GRANITE_AVAILABLE = True
except ImportError:
    GRANITE_AVAILABLE = False
    logger.warning("⚠️  Transformers not available. Install with: pip install transformers torchaudio soundfile")


class GraniteTranscriber:
    """IBM Granite Speech model wrapper for transcription."""

    def __init__(self, config: Dict[str, Any]):
        """Initialize Granite transcriber with configuration."""
        self.config = config
        self.model = None
        self.processor = None
        self.tokenizer = None
        self.device = torch.device(config.get("device", "cuda" if torch.cuda.is_available() else "cpu"))
        self.model_name = "ibm-granite/granite-speech-3.3-2b"

    def load_model(self) -> bool:
        """Load IBM Granite Speech model."""
        if not GRANITE_AVAILABLE:
            logger.error("❌ Transformers not available. Cannot load Granite Speech model.")
            return False

        try:
            logger.info("🔄 Loading IBM Granite-speech-3.3-2b model...")

            # Load processor and model
            self.processor = AutoProcessor.from_pretrained(self.model_name)
            self.tokenizer = self.processor.tokenizer
            self.model = AutoModelForSpeechSeq2Seq.from_pretrained(self.model_name)

            # Move to specified device
            self.model = self.model.to(self.device)
            self.model.eval()

            logger.info(f"✅ Granite Speech model loaded successfully on {self.device}")
            return True

        except Exception as e:
            logger.error(f"❌ Failed to load Granite Speech model: {str(e)}")
            return False
    
    def unload_model(self) -> None:
        """Unload model to free memory."""
        if self.model is not None:
            del self.model
            self.model = None

        if self.processor is not None:
            del self.processor
            self.processor = None

        if self.tokenizer is not None:
            del self.tokenizer
            self.tokenizer = None

        if self.device.type == "cuda":
            torch.cuda.empty_cache()

        logger.info("🧹 Granite Speech model unloaded")
    
    def _load_and_preprocess_audio(self, audio_path: Path) -> Optional[torch.Tensor]:
        """Load and preprocess audio file for Granite Speech."""
        try:
            # Load audio file
            wav, sr = torchaudio.load(str(audio_path), normalize=True)

            # Ensure mono audio
            if wav.shape[0] > 1:
                wav = wav.mean(dim=0, keepdim=True)

            # Resample to 16kHz if needed
            if sr != 16000:
                resampler = torchaudio.transforms.Resample(sr, 16000)
                wav = resampler(wav)

            # Ensure single channel
            assert wav.shape[0] == 1, f"Audio must be mono, got {wav.shape[0]} channels"

            return wav.squeeze(0)  # Remove channel dimension

        except Exception as e:
            logger.error(f"❌ Failed to load audio {audio_path}: {str(e)}")
            return None

    def _create_chat_prompt(self) -> str:
        """Create the chat prompt for Granite Speech transcription."""
        chat = [
            {
                "role": "system",
                "content": "Knowledge Cutoff Date: April 2024.\nToday's Date: May 2, 2025.\nYou are Granite, developed by IBM. You are a helpful AI assistant",
            },
            {
                "role": "user",
                "content": "<|audio|>can you transcribe the speech into a written format?",
            }
        ]

        return self.tokenizer.apply_chat_template(
            chat, tokenize=False, add_generation_prompt=True
        )

    def transcribe_batch(self, audio_files: List[Path]) -> List[Dict[str, Any]]:
        """
        Transcribe a batch of audio files using Granite Speech.

        Args:
            audio_files: List of audio file paths

        Returns:
            List of transcription results with timing information
        """
        if not self.model or not self.processor:
            logger.error("❌ Model not loaded. Call load_model() first.")
            return []

        results = []

        try:
            logger.info(f"🎙️  Transcribing {len(audio_files)} audio files with Granite Speech...")

            # Get generation parameters from config
            num_beams = self.config.get("granite_num_beams", 4)  # Recommended > 1
            max_new_tokens = self.config.get("granite_max_tokens", 200)
            temperature = self.config.get("granite_temperature", 1.0)

            # Create chat prompt once
            text_prompt = self._create_chat_prompt()

            # Transcribe with progress bar
            with tqdm(total=len(audio_files), desc="Transcribing") as pbar:
                # Process files individually for better error handling
                for audio_file in audio_files:
                    try:
                        # Load and preprocess audio
                        wav = self._load_and_preprocess_audio(audio_file)
                        if wav is None:
                            raise Exception("Failed to load audio file")

                        # Prepare model inputs
                        model_inputs = self.processor(
                            text_prompt,
                            wav,
                            device=self.device,
                            return_tensors="pt",
                        ).to(self.device)

                        # Generate transcription
                        with torch.no_grad():
                            model_outputs = self.model.generate(
                                **model_inputs,
                                max_new_tokens=max_new_tokens,
                                num_beams=num_beams,
                                do_sample=False,
                                min_length=1,
                                top_p=1.0,
                                repetition_penalty=1.0,
                                length_penalty=1.0,
                                temperature=temperature,
                                bos_token_id=self.tokenizer.bos_token_id,
                                eos_token_id=self.tokenizer.eos_token_id,
                                pad_token_id=self.tokenizer.pad_token_id,
                            )

                        # Extract only the new tokens (exclude input)
                        num_input_tokens = model_inputs["input_ids"].shape[-1]
                        new_tokens = torch.unsqueeze(model_outputs[0, num_input_tokens:], dim=0)

                        # Decode transcription
                        transcription = self.tokenizer.batch_decode(
                            new_tokens, add_special_tokens=False, skip_special_tokens=True
                        )[0].strip()

                        result = {
                            "audio_file": str(audio_file),
                            "transcription": transcription,
                            "filename": audio_file.name,
                            "stem": audio_file.stem,
                            "success": True,
                            "error": None
                        }

                        # Extract timing information from filename if available
                        timing_info = self._extract_timing_from_filename(audio_file.name)
                        if timing_info:
                            result.update(timing_info)

                        results.append(result)
                        pbar.update(1)

                        # Clear cache after each file
                        if self.device.type == "cuda":
                            torch.cuda.empty_cache()

                    except Exception as e:
                        logger.error(f"❌ Transcription failed for {audio_file.name}: {str(e)}")

                        result = {
                            "audio_file": str(audio_file),
                            "transcription": "",
                            "filename": audio_file.name,
                            "stem": audio_file.stem,
                            "success": False,
                            "error": str(e)
                        }
                        results.append(result)
                        pbar.update(1)

            successful = sum(1 for r in results if r["success"])
            logger.info(f"✅ Granite Speech transcription completed: {successful}/{len(results)} successful")

            return results

        except Exception as e:
            logger.error(f"❌ Granite Speech transcription error: {str(e)}")
            return []
    
    def _extract_timing_from_filename(self, filename: str) -> Optional[Dict[str, float]]:
        """
        Extract timing information from segment filename.
        
        Expected format: seg_001_speaker_1.23s-4.56s.wav
        """
        try:
            # Look for timing pattern in filename
            import re
            pattern = r'(\d+\.?\d*)s-(\d+\.?\d*)s'
            match = re.search(pattern, filename)
            
            if match:
                start_time = float(match.group(1))
                end_time = float(match.group(2))
                
                return {
                    "start_time": start_time,
                    "end_time": end_time,
                    "duration": end_time - start_time
                }
                
        except Exception as e:
            logger.debug(f"Could not extract timing from filename {filename}: {str(e)}")
            
        return None


def transcribe_with_granite(
    audio_files: List[Path],
    output_dir: Path,
    config: Dict[str, Any]
) -> List[Dict[str, Any]]:
    """
    Transcribe audio files using IBM Granite Speech model.

    Args:
        audio_files: List of audio files to transcribe
        output_dir: Directory to save transcription results
        config: Configuration dictionary

    Returns:
        List of transcription results
    """
    if not audio_files:
        logger.warning("⚠️  No audio files provided for transcription")
        return []

    logger.info(f"🎙️  Starting Granite Speech transcription for {len(audio_files)} files")

    # Initialize transcriber
    transcriber = GraniteTranscriber(config)

    try:
        # Load model
        if not transcriber.load_model():
            logger.error("❌ Failed to load Granite Speech model")
            return []

        # Transcribe files
        results = transcriber.transcribe_batch(audio_files)

        # Save results to JSON file
        results_file = output_dir / "granite_transcriptions.json"
        with open(results_file, 'w', encoding='utf-8') as f:
            json.dump(results, f, indent=2, ensure_ascii=False)

        logger.info(f"💾 Transcription results saved to: {results_file}")

        # Also save as TSV for compatibility
        tsv_file = output_dir / "audio_list.tsv"
        save_transcriptions_as_tsv(results, tsv_file)

        return results

    except Exception as e:
        logger.error(f"❌ Granite Speech transcription failed: {str(e)}")
        return []

    finally:
        # Always unload model to free memory
        transcriber.unload_model()


# Keep the old function name for backward compatibility
def transcribe_with_canary(
    audio_files: List[Path],
    output_dir: Path,
    config: Dict[str, Any]
) -> List[Dict[str, Any]]:
    """
    Backward compatibility wrapper for transcribe_with_granite.

    Args:
        audio_files: List of audio files to transcribe
        output_dir: Directory to save transcription results
        config: Configuration dictionary

    Returns:
        List of transcription results
    """
    logger.warning("⚠️  transcribe_with_canary is deprecated. Use transcribe_with_granite instead.")
    return transcribe_with_granite(audio_files, output_dir, config)


def save_transcriptions_as_tsv(results: List[Dict[str, Any]], output_file: Path) -> None:
    """Save transcription results as TSV file for compatibility."""
    try:
        with open(output_file, 'w', encoding='utf-8') as f:
            f.write("wav\tdecode\n")  # Header
            
            for result in results:
                if result["success"]:
                    audio_path = result["audio_file"].replace('\\', '/')
                    transcription = result["transcription"].replace('\t', ' ').replace('\n', ' ')
                    f.write(f"{audio_path}\t{transcription}\n")
        
        logger.info(f"💾 TSV transcriptions saved to: {output_file}")
        
    except Exception as e:
        logger.error(f"❌ Failed to save TSV transcriptions: {str(e)}")


def test_granite_model() -> bool:
    """Test if Granite Speech model can be loaded and used."""
    if not GRANITE_AVAILABLE:
        logger.error("❌ Transformers not available")
        return False

    try:
        logger.info("🧪 Testing Granite Speech model...")

        # Create test configuration
        config = {
            "device": "cuda" if torch.cuda.is_available() else "cpu",
            "granite_num_beams": 4,
            "granite_max_tokens": 50,
            "granite_temperature": 1.0
        }

        # Initialize transcriber
        transcriber = GraniteTranscriber(config)

        # Try to load model
        if transcriber.load_model():
            logger.info("✅ Granite Speech model test successful")
            transcriber.unload_model()
            return True
        else:
            logger.error("❌ Granite Speech model test failed")
            return False

    except Exception as e:
        logger.error(f"❌ Granite Speech model test error: {str(e)}")
        return False


def test_canary_model() -> bool:
    """Backward compatibility test function."""
    logger.warning("⚠️  test_canary_model is deprecated. Use test_granite_model instead.")
    return test_granite_model()


if __name__ == "__main__":
    # Test the Granite Speech model
    test_granite_model()
