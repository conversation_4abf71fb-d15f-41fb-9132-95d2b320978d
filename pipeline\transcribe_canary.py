"""
NVIDIA Canary Transcription Module for AutoSub with LLM

This module provides transcription functionality using the NVIDIA Canary model,
replacing the standard ChunkFormer model for higher quality ASR results.

Features:
- NVIDIA Canary-1B-Flash model integration
- Batch processing with memory management
- Timestamp preservation for SRT generation
- Error handling and fallback mechanisms
"""

import os
import sys
import torch
import logging
from pathlib import Path
from typing import List, Dict, Any, Optional, Union
import tempfile
import json
from tqdm import tqdm

# Setup logging
logger = logging.getLogger(__name__)

try:
    import nemo.collections.asr as nemo_asr
    NEMO_AVAILABLE = True
except ImportError:
    NEMO_AVAILABLE = False
    logger.warning("⚠️  NeMo not available. Install with: pip install nemo_toolkit[asr]")


class CanaryTranscriber:
    """NVIDIA Canary model wrapper for transcription."""
    
    def __init__(self, config: Dict[str, Any]):
        """Initialize Canary transcriber with configuration."""
        self.config = config
        self.model = None
        self.device = torch.device(config.get("device", "cuda" if torch.cuda.is_available() else "cpu"))
        
    def load_model(self) -> bool:
        """Load NVIDIA Canary model."""
        if not NEMO_AVAILABLE:
            logger.error("❌ NeMo toolkit not available. Cannot load Canary model.")
            return False
            
        try:
            logger.info("🔄 Loading NVIDIA Canary-1B-Flash model...")
            
            # Load the model
            self.model = nemo_asr.models.ASRModel.from_pretrained("nvidia/canary-1b-flash")
            
            # Move to specified device
            self.model = self.model.to(self.device)
            self.model.eval()
            
            logger.info(f"✅ Canary model loaded successfully on {self.device}")
            return True
            
        except Exception as e:
            logger.error(f"❌ Failed to load Canary model: {str(e)}")
            return False
    
    def unload_model(self) -> None:
        """Unload model to free memory."""
        if self.model is not None:
            del self.model
            self.model = None
            
            if self.device.type == "cuda":
                torch.cuda.empty_cache()
                
            logger.info("🧹 Canary model unloaded")
    
    def transcribe_batch(self, audio_files: List[Path]) -> List[Dict[str, Any]]:
        """
        Transcribe a batch of audio files.
        
        Args:
            audio_files: List of audio file paths
            
        Returns:
            List of transcription results with timing information
        """
        if not self.model:
            logger.error("❌ Model not loaded. Call load_model() first.")
            return []
        
        results = []
        
        try:
            # Convert paths to strings
            audio_paths = [str(audio_file) for audio_file in audio_files]
            
            logger.info(f"🎙️  Transcribing {len(audio_paths)} audio files with Canary...")
            
            # Transcribe with progress bar
            with tqdm(total=len(audio_paths), desc="Transcribing") as pbar:
                # Process in smaller batches to manage memory
                batch_size = self.config.get("canary_batch_size", 4)
                
                for i in range(0, len(audio_paths), batch_size):
                    batch_paths = audio_paths[i:i + batch_size]
                    batch_files = audio_files[i:i + batch_size]
                    
                    try:
                        # Transcribe batch
                        transcriptions = self.model.transcribe(batch_paths)
                        
                        # Process results
                        for j, (transcription, audio_file) in enumerate(zip(transcriptions, batch_files)):
                            result = {
                                "audio_file": str(audio_file),
                                "transcription": transcription,
                                "filename": audio_file.name,
                                "stem": audio_file.stem,
                                "success": True,
                                "error": None
                            }
                            
                            # Extract timing information from filename if available
                            timing_info = self._extract_timing_from_filename(audio_file.name)
                            if timing_info:
                                result.update(timing_info)
                            
                            results.append(result)
                            pbar.update(1)
                            
                    except Exception as e:
                        logger.error(f"❌ Batch transcription failed: {str(e)}")
                        
                        # Add failed results for this batch
                        for audio_file in batch_files:
                            result = {
                                "audio_file": str(audio_file),
                                "transcription": "",
                                "filename": audio_file.name,
                                "stem": audio_file.stem,
                                "success": False,
                                "error": str(e)
                            }
                            results.append(result)
                            pbar.update(1)
                    
                    # Clear cache between batches
                    if self.device.type == "cuda":
                        torch.cuda.empty_cache()
            
            successful = sum(1 for r in results if r["success"])
            logger.info(f"✅ Canary transcription completed: {successful}/{len(results)} successful")
            
            return results
            
        except Exception as e:
            logger.error(f"❌ Canary transcription error: {str(e)}")
            return []
    
    def _extract_timing_from_filename(self, filename: str) -> Optional[Dict[str, float]]:
        """
        Extract timing information from segment filename.
        
        Expected format: seg_001_speaker_1.23s-4.56s.wav
        """
        try:
            # Look for timing pattern in filename
            import re
            pattern = r'(\d+\.?\d*)s-(\d+\.?\d*)s'
            match = re.search(pattern, filename)
            
            if match:
                start_time = float(match.group(1))
                end_time = float(match.group(2))
                
                return {
                    "start_time": start_time,
                    "end_time": end_time,
                    "duration": end_time - start_time
                }
                
        except Exception as e:
            logger.debug(f"Could not extract timing from filename {filename}: {str(e)}")
            
        return None


def transcribe_with_canary(
    audio_files: List[Path],
    output_dir: Path,
    config: Dict[str, Any]
) -> List[Dict[str, Any]]:
    """
    Transcribe audio files using NVIDIA Canary model.
    
    Args:
        audio_files: List of audio files to transcribe
        output_dir: Directory to save transcription results
        config: Configuration dictionary
        
    Returns:
        List of transcription results
    """
    if not audio_files:
        logger.warning("⚠️  No audio files provided for transcription")
        return []
    
    logger.info(f"🎙️  Starting Canary transcription for {len(audio_files)} files")
    
    # Initialize transcriber
    transcriber = CanaryTranscriber(config)
    
    try:
        # Load model
        if not transcriber.load_model():
            logger.error("❌ Failed to load Canary model")
            return []
        
        # Transcribe files
        results = transcriber.transcribe_batch(audio_files)
        
        # Save results to JSON file
        results_file = output_dir / "canary_transcriptions.json"
        with open(results_file, 'w', encoding='utf-8') as f:
            json.dump(results, f, indent=2, ensure_ascii=False)
        
        logger.info(f"💾 Transcription results saved to: {results_file}")
        
        # Also save as TSV for compatibility
        tsv_file = output_dir / "audio_list.tsv"
        save_transcriptions_as_tsv(results, tsv_file)
        
        return results
        
    except Exception as e:
        logger.error(f"❌ Canary transcription failed: {str(e)}")
        return []
        
    finally:
        # Always unload model to free memory
        transcriber.unload_model()


def save_transcriptions_as_tsv(results: List[Dict[str, Any]], output_file: Path) -> None:
    """Save transcription results as TSV file for compatibility."""
    try:
        with open(output_file, 'w', encoding='utf-8') as f:
            f.write("wav\tdecode\n")  # Header
            
            for result in results:
                if result["success"]:
                    audio_path = result["audio_file"].replace('\\', '/')
                    transcription = result["transcription"].replace('\t', ' ').replace('\n', ' ')
                    f.write(f"{audio_path}\t{transcription}\n")
        
        logger.info(f"💾 TSV transcriptions saved to: {output_file}")
        
    except Exception as e:
        logger.error(f"❌ Failed to save TSV transcriptions: {str(e)}")


def test_canary_model() -> bool:
    """Test if Canary model can be loaded and used."""
    if not NEMO_AVAILABLE:
        logger.error("❌ NeMo toolkit not available")
        return False
    
    try:
        logger.info("🧪 Testing Canary model...")
        
        # Create test configuration
        config = {
            "device": "cuda" if torch.cuda.is_available() else "cpu",
            "canary_batch_size": 1
        }
        
        # Initialize transcriber
        transcriber = CanaryTranscriber(config)
        
        # Try to load model
        if transcriber.load_model():
            logger.info("✅ Canary model test successful")
            transcriber.unload_model()
            return True
        else:
            logger.error("❌ Canary model test failed")
            return False
            
    except Exception as e:
        logger.error(f"❌ Canary model test error: {str(e)}")
        return False


if __name__ == "__main__":
    # Test the Canary model
    test_canary_model()
