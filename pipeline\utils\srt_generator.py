"""
SRT Generator for AutoSub with LLM

This module generates SRT subtitle files from enhanced transcriptions with
precise video synchronization and proper formatting.

Features:
- SRT file generation with proper formatting
- Video synchronization and timing validation
- Multiple subtitle tracks (by speaker, combined, etc.)
- Text formatting and line breaking
- Subtitle validation and quality checks
"""

import os
import logging
from pathlib import Path
from typing import List, Dict, Any, Optional, Union
import re
import textwrap

# Setup logging
logger = logging.getLogger(__name__)

# Import timing utilities
from .timing_utils import synchronize_with_video, format_srt_timestamp, validate_timing_sequence, create_timing_report


def generate_srt_files(
    enhanced_transcriptions: List[Dict[str, Any]],
    timing_info: Dict[str, Any],
    video_path: Path,
    output_dir: Path,
    config: Dict[str, Any]
) -> List[Path]:
    """
    Generate SRT subtitle files from enhanced transcriptions.
    
    Args:
        enhanced_transcriptions: List of LLM-enhanced transcriptions
        timing_info: Timing information from diarization
        video_path: Original video file path
        output_dir: Directory to save SRT files
        config: Configuration dictionary
        
    Returns:
        List of generated SRT file paths
    """
    logger.info(f"📝 Generating SRT files for {video_path.name}")
    
    try:
        # Synchronize transcriptions with video timing
        synchronized_transcriptions = synchronize_with_video(
            enhanced_transcriptions, timing_info, video_path
        )
        
        # Validate and correct timing sequence
        validated_transcriptions = validate_timing_sequence(synchronized_transcriptions)
        
        # Create timing report for debugging
        timing_report_file = output_dir / f"{video_path.stem}_timing_report.json"
        create_timing_report(validated_transcriptions, timing_report_file)
        
        # Generate different types of SRT files
        srt_files = []
        
        # 1. Combined SRT (all speakers)
        combined_srt = generate_combined_srt(validated_transcriptions, output_dir, video_path.stem, config)
        if combined_srt:
            srt_files.append(combined_srt)
        
        # 2. Speaker-specific SRTs (if multiple speakers)
        speaker_srts = generate_speaker_specific_srts(validated_transcriptions, output_dir, video_path.stem, config)
        srt_files.extend(speaker_srts)
        
        # 3. Enhanced vs Original comparison (if requested)
        if config.get("generate_comparison_srt", False):
            comparison_srt = generate_comparison_srt(validated_transcriptions, output_dir, video_path.stem, config)
            if comparison_srt:
                srt_files.append(comparison_srt)
        
        logger.info(f"✅ Generated {len(srt_files)} SRT files")
        return srt_files
        
    except Exception as e:
        logger.error(f"❌ SRT generation failed: {str(e)}")
        return []


def generate_combined_srt(
    transcriptions: List[Dict[str, Any]],
    output_dir: Path,
    video_stem: str,
    config: Dict[str, Any]
) -> Optional[Path]:
    """Generate combined SRT file with all speakers."""
    try:
        # Filter valid transcriptions
        valid_transcriptions = [
            t for t in transcriptions 
            if t.get("success", False) and t.get("synchronized", False) and t.get("transcription", "").strip()
        ]
        
        if not valid_transcriptions:
            logger.warning("⚠️  No valid transcriptions for combined SRT")
            return None
        
        # Sort by start time
        valid_transcriptions.sort(key=lambda x: x.get("start_time", 0))
        
        # Generate SRT content
        srt_content = []
        subtitle_index = 1
        
        for transcription in valid_transcriptions:
            # Format subtitle entry
            start_time = format_srt_timestamp(transcription["start_time"])
            end_time = format_srt_timestamp(transcription["end_time"])
            text = transcription["transcription"].strip()
            
            # Add speaker label if configured
            if config.get("include_speaker_labels", True):
                speaker = transcription.get("speaker", "unknown")
                if speaker != "unknown":
                    text = f"[{speaker}] {text}"
            
            # Format text with line breaks
            formatted_text = format_subtitle_text(text, config)
            
            # Create SRT entry
            srt_entry = f"{subtitle_index}\n{start_time} --> {end_time}\n{formatted_text}\n"
            srt_content.append(srt_entry)
            subtitle_index += 1
        
        # Save SRT file
        srt_file = output_dir / f"{video_stem}.srt"
        with open(srt_file, 'w', encoding='utf-8') as f:
            f.write('\n'.join(srt_content))
        
        logger.info(f"💾 Combined SRT saved: {srt_file} ({len(valid_transcriptions)} subtitles)")
        return srt_file
        
    except Exception as e:
        logger.error(f"❌ Failed to generate combined SRT: {str(e)}")
        return None


def generate_speaker_specific_srts(
    transcriptions: List[Dict[str, Any]],
    output_dir: Path,
    video_stem: str,
    config: Dict[str, Any]
) -> List[Path]:
    """Generate separate SRT files for each speaker."""
    try:
        # Group transcriptions by speaker
        speaker_groups = {}
        
        for transcription in transcriptions:
            if not (transcription.get("success", False) and transcription.get("synchronized", False)):
                continue
            
            speaker = transcription.get("speaker", "unknown")
            if speaker not in speaker_groups:
                speaker_groups[speaker] = []
            speaker_groups[speaker].append(transcription)
        
        # Only generate speaker-specific SRTs if there are multiple speakers
        if len(speaker_groups) <= 1:
            logger.info("📝 Only one speaker detected, skipping speaker-specific SRTs")
            return []
        
        srt_files = []
        
        for speaker, speaker_transcriptions in speaker_groups.items():
            if not speaker_transcriptions:
                continue
            
            # Sort by start time
            speaker_transcriptions.sort(key=lambda x: x.get("start_time", 0))
            
            # Generate SRT content for this speaker
            srt_content = []
            subtitle_index = 1
            
            for transcription in speaker_transcriptions:
                text = transcription["transcription"].strip()
                if not text:
                    continue
                
                start_time = format_srt_timestamp(transcription["start_time"])
                end_time = format_srt_timestamp(transcription["end_time"])
                
                # Format text
                formatted_text = format_subtitle_text(text, config)
                
                # Create SRT entry
                srt_entry = f"{subtitle_index}\n{start_time} --> {end_time}\n{formatted_text}\n"
                srt_content.append(srt_entry)
                subtitle_index += 1
            
            # Save speaker-specific SRT file
            safe_speaker = re.sub(r'[^\w\-_]', '_', speaker)  # Make filename safe
            srt_file = output_dir / f"{video_stem}_{safe_speaker}.srt"
            
            with open(srt_file, 'w', encoding='utf-8') as f:
                f.write('\n'.join(srt_content))
            
            logger.info(f"💾 Speaker SRT saved: {srt_file} ({len(speaker_transcriptions)} subtitles)")
            srt_files.append(srt_file)
        
        return srt_files
        
    except Exception as e:
        logger.error(f"❌ Failed to generate speaker-specific SRTs: {str(e)}")
        return []


def generate_comparison_srt(
    transcriptions: List[Dict[str, Any]],
    output_dir: Path,
    video_stem: str,
    config: Dict[str, Any]
) -> Optional[Path]:
    """Generate comparison SRT showing original vs enhanced transcriptions."""
    try:
        # Filter transcriptions that have both original and enhanced versions
        comparison_transcriptions = [
            t for t in transcriptions 
            if (t.get("success", False) and t.get("synchronized", False) and 
                t.get("original_transcription") and t.get("transcription"))
        ]
        
        if not comparison_transcriptions:
            logger.warning("⚠️  No transcriptions with original/enhanced comparison available")
            return None
        
        # Sort by start time
        comparison_transcriptions.sort(key=lambda x: x.get("start_time", 0))
        
        # Generate comparison SRT content
        srt_content = []
        subtitle_index = 1
        
        for transcription in comparison_transcriptions:
            start_time = format_srt_timestamp(transcription["start_time"])
            end_time = format_srt_timestamp(transcription["end_time"])
            
            original_text = transcription["original_transcription"].strip()
            enhanced_text = transcription["transcription"].strip()
            
            # Create comparison text
            comparison_text = f"Original: {original_text}\nEnhanced: {enhanced_text}"
            
            # Format text
            formatted_text = format_subtitle_text(comparison_text, config)
            
            # Create SRT entry
            srt_entry = f"{subtitle_index}\n{start_time} --> {end_time}\n{formatted_text}\n"
            srt_content.append(srt_entry)
            subtitle_index += 1
        
        # Save comparison SRT file
        srt_file = output_dir / f"{video_stem}_comparison.srt"
        with open(srt_file, 'w', encoding='utf-8') as f:
            f.write('\n'.join(srt_content))
        
        logger.info(f"💾 Comparison SRT saved: {srt_file} ({len(comparison_transcriptions)} subtitles)")
        return srt_file
        
    except Exception as e:
        logger.error(f"❌ Failed to generate comparison SRT: {str(e)}")
        return None


def format_subtitle_text(text: str, config: Dict[str, Any]) -> str:
    """
    Format subtitle text with proper line breaks and length limits.
    
    Args:
        text: Raw subtitle text
        config: Configuration dictionary
        
    Returns:
        Formatted subtitle text
    """
    try:
        # Get configuration
        max_line_length = config.get("srt_max_line_length", 42)
        max_lines = config.get("srt_max_lines", 2)
        
        # Clean text
        text = text.strip()
        text = re.sub(r'\s+', ' ', text)  # Normalize whitespace
        
        # Break into lines
        if len(text) <= max_line_length:
            return text
        
        # Use textwrap for intelligent line breaking
        lines = textwrap.wrap(text, width=max_line_length, break_long_words=False, break_on_hyphens=False)
        
        # Limit number of lines
        if len(lines) > max_lines:
            # Truncate and add ellipsis
            lines = lines[:max_lines]
            if lines:
                lines[-1] = lines[-1][:max_line_length-3] + "..."
        
        return '\n'.join(lines)
        
    except Exception as e:
        logger.error(f"❌ Failed to format subtitle text: {str(e)}")
        return text


def validate_srt_file(srt_file: Path) -> Dict[str, Any]:
    """
    Validate SRT file format and content.
    
    Args:
        srt_file: Path to SRT file
        
    Returns:
        Validation results dictionary
    """
    validation_result = {
        "valid": False,
        "subtitle_count": 0,
        "total_duration": 0,
        "errors": [],
        "warnings": []
    }
    
    try:
        if not srt_file.exists():
            validation_result["errors"].append("SRT file does not exist")
            return validation_result
        
        with open(srt_file, 'r', encoding='utf-8') as f:
            content = f.read()
        
        # Parse SRT content
        subtitle_blocks = content.strip().split('\n\n')
        subtitle_count = 0
        last_end_time = 0
        
        for block in subtitle_blocks:
            lines = block.strip().split('\n')
            
            if len(lines) < 3:
                validation_result["warnings"].append(f"Incomplete subtitle block: {block[:50]}...")
                continue
            
            # Validate subtitle number
            try:
                subtitle_num = int(lines[0])
                subtitle_count += 1
            except ValueError:
                validation_result["errors"].append(f"Invalid subtitle number: {lines[0]}")
                continue
            
            # Validate timing line
            timing_line = lines[1]
            if ' --> ' not in timing_line:
                validation_result["errors"].append(f"Invalid timing format: {timing_line}")
                continue
            
            try:
                start_str, end_str = timing_line.split(' --> ')
                start_time = parse_srt_timestamp(start_str)
                end_time = parse_srt_timestamp(end_str)
                
                # Check timing order
                if start_time >= end_time:
                    validation_result["errors"].append(f"Invalid timing order in subtitle {subtitle_num}")
                
                if start_time < last_end_time:
                    validation_result["warnings"].append(f"Overlapping subtitles at {subtitle_num}")
                
                last_end_time = end_time
                validation_result["total_duration"] = max(validation_result["total_duration"], end_time)
                
            except Exception as e:
                validation_result["errors"].append(f"Failed to parse timing in subtitle {subtitle_num}: {str(e)}")
        
        validation_result["subtitle_count"] = subtitle_count
        validation_result["valid"] = len(validation_result["errors"]) == 0
        
        logger.info(f"📋 SRT validation: {subtitle_count} subtitles, {len(validation_result['errors'])} errors, {len(validation_result['warnings'])} warnings")
        
        return validation_result
        
    except Exception as e:
        validation_result["errors"].append(f"Failed to validate SRT file: {str(e)}")
        return validation_result


def parse_srt_timestamp(timestamp_str: str) -> float:
    """Parse SRT timestamp string to seconds."""
    try:
        # Format: HH:MM:SS,mmm
        time_part, ms_part = timestamp_str.split(',')
        hours, minutes, seconds = map(int, time_part.split(':'))
        milliseconds = int(ms_part)
        
        total_seconds = hours * 3600 + minutes * 60 + seconds + milliseconds / 1000.0
        return total_seconds
        
    except Exception as e:
        raise ValueError(f"Invalid SRT timestamp format: {timestamp_str}")


if __name__ == "__main__":
    # Test SRT utilities
    logger.info("🧪 Testing SRT utilities...")
    
    # Test text formatting
    test_text = "This is a very long subtitle text that should be broken into multiple lines for better readability."
    config = {"srt_max_line_length": 42, "srt_max_lines": 2}
    formatted = format_subtitle_text(test_text, config)
    logger.info(f"Formatted text:\n{formatted}")
    
    # Test timestamp parsing
    test_timestamps = ["00:01:23,456", "01:30:45,789"]
    for ts in test_timestamps:
        seconds = parse_srt_timestamp(ts)
        logger.info(f"{ts} -> {seconds}s")
    
    logger.info("✅ SRT utilities test completed")
