"""
Timing Utilities for AutoSub with LLM

This module provides utilities for handling timing information, synchronization
with video files, and preparing data for SRT generation.

Features:
- Video duration extraction
- Timing synchronization between segments and transcriptions
- SRT timestamp formatting
- Segment timing validation and correction
"""

import os
import logging
from pathlib import Path
from typing import List, Dict, Any, Optional, Tuple, Union
import subprocess
import json
from datetime import timedelta

# Setup logging
logger = logging.getLogger(__name__)

try:
    from pydub import AudioSegment
    PYDUB_AVAILABLE = True
except ImportError:
    PYDUB_AVAILABLE = False
    logger.warning("⚠️  pydub not available. Install with: pip install pydub")

try:
    import librosa
    LIBROSA_AVAILABLE = True
except ImportError:
    LIBROSA_AVAILABLE = False
    logger.warning("⚠️  librosa not available. Install with: pip install librosa")


def get_video_duration(video_path: Union[str, Path]) -> Optional[float]:
    """
    Get video duration in seconds using ffprobe.
    
    Args:
        video_path: Path to video file
        
    Returns:
        Duration in seconds, or None if failed
    """
    try:
        video_path = Path(video_path)
        
        # Try ffprobe first (most accurate)
        if _has_ffprobe():
            return _get_duration_ffprobe(video_path)
        
        # Fallback to pydub
        if PYDUB_AVAILABLE:
            return _get_duration_pydub(video_path)
        
        # Fallback to librosa for audio files
        if LIBROSA_AVAILABLE and video_path.suffix.lower() in ['.wav', '.mp3', '.flac', '.m4a', '.ogg']:
            return _get_duration_librosa(video_path)
        
        logger.error(f"❌ No method available to get duration for {video_path}")
        return None
        
    except Exception as e:
        logger.error(f"❌ Failed to get video duration: {str(e)}")
        return None


def _has_ffprobe() -> bool:
    """Check if ffprobe is available."""
    try:
        subprocess.run(['ffprobe', '-version'], capture_output=True, check=True)
        return True
    except (subprocess.CalledProcessError, FileNotFoundError):
        return False


def _get_duration_ffprobe(video_path: Path) -> Optional[float]:
    """Get duration using ffprobe."""
    try:
        cmd = [
            'ffprobe',
            '-v', 'quiet',
            '-print_format', 'json',
            '-show_format',
            str(video_path)
        ]
        
        result = subprocess.run(cmd, capture_output=True, text=True, check=True)
        data = json.loads(result.stdout)
        
        duration = float(data['format']['duration'])
        logger.debug(f"Video duration (ffprobe): {duration:.2f}s")
        return duration
        
    except Exception as e:
        logger.debug(f"ffprobe failed: {str(e)}")
        return None


def _get_duration_pydub(video_path: Path) -> Optional[float]:
    """Get duration using pydub."""
    try:
        audio = AudioSegment.from_file(str(video_path))
        duration = len(audio) / 1000.0  # Convert ms to seconds
        logger.debug(f"Video duration (pydub): {duration:.2f}s")
        return duration
        
    except Exception as e:
        logger.debug(f"pydub failed: {str(e)}")
        return None


def _get_duration_librosa(audio_path: Path) -> Optional[float]:
    """Get duration using librosa."""
    try:
        y, sr = librosa.load(str(audio_path))
        duration = len(y) / sr
        logger.debug(f"Audio duration (librosa): {duration:.2f}s")
        return duration
        
    except Exception as e:
        logger.debug(f"librosa failed: {str(e)}")
        return None


def synchronize_with_video(
    enhanced_transcriptions: List[Dict[str, Any]],
    timing_info: Dict[str, Any],
    video_path: Path
) -> List[Dict[str, Any]]:
    """
    Synchronize enhanced transcriptions with original video timing.
    
    Args:
        enhanced_transcriptions: List of enhanced transcription results
        timing_info: Timing information from diarization
        video_path: Original video file path
        
    Returns:
        List of synchronized transcription results with accurate timing
    """
    logger.info(f"🔄 Synchronizing transcriptions with video: {video_path.name}")
    
    try:
        # Get video duration for validation
        video_duration = get_video_duration(video_path)
        if video_duration:
            logger.info(f"📹 Video duration: {video_duration:.2f}s")
        
        # Get segment timing information
        segments = timing_info.get("segments", [])
        if not segments:
            logger.warning("⚠️  No segment timing information available")
            return enhanced_transcriptions
        
        synchronized_results = []
        
        for transcription in enhanced_transcriptions:
            if not transcription.get("success", False):
                synchronized_results.append(transcription)
                continue
            
            # Find matching segment timing
            segment_timing = _find_matching_segment(transcription, segments)
            
            if segment_timing:
                # Add synchronized timing
                sync_result = transcription.copy()
                sync_result.update({
                    "start_time": segment_timing["start_time"],
                    "end_time": segment_timing["end_time"],
                    "duration": segment_timing["duration"],
                    "speaker": segment_timing.get("speaker", "unknown"),
                    "synchronized": True
                })
                
                # Validate timing against video duration
                if video_duration and sync_result["end_time"] > video_duration:
                    logger.warning(f"⚠️  Segment end time ({sync_result['end_time']:.2f}s) exceeds video duration ({video_duration:.2f}s)")
                    sync_result["end_time"] = video_duration
                    sync_result["duration"] = sync_result["end_time"] - sync_result["start_time"]
                
                synchronized_results.append(sync_result)
            else:
                logger.warning(f"⚠️  No matching segment found for transcription: {transcription.get('filename', 'unknown')}")
                synchronized_results.append(transcription)
        
        synchronized_count = sum(1 for r in synchronized_results if r.get("synchronized", False))
        logger.info(f"✅ Synchronization completed: {synchronized_count}/{len(enhanced_transcriptions)} synchronized")
        
        return synchronized_results
        
    except Exception as e:
        logger.error(f"❌ Synchronization failed: {str(e)}")
        return enhanced_transcriptions


def _find_matching_segment(transcription: Dict[str, Any], segments: List[Dict[str, Any]]) -> Optional[Dict[str, Any]]:
    """Find matching segment timing for a transcription."""
    try:
        filename = transcription.get("filename", "")
        
        # Try to extract timing from filename first
        if "start_time" in transcription and "end_time" in transcription:
            return {
                "start_time": transcription["start_time"],
                "end_time": transcription["end_time"],
                "duration": transcription.get("duration", transcription["end_time"] - transcription["start_time"]),
                "speaker": transcription.get("speaker", "unknown")
            }
        
        # Try to match by filename pattern
        for segment in segments:
            # Check if filename contains segment timing
            start_time = segment.get("start_time", 0)
            end_time = segment.get("end_time", 0)
            
            # Look for timing pattern in filename
            if f"{start_time:.2f}s-{end_time:.2f}s" in filename:
                return segment
        
        # If no exact match, try to find by segment index
        import re
        seg_match = re.search(r'seg_(\d+)', filename)
        if seg_match:
            seg_index = int(seg_match.group(1))
            if 0 <= seg_index < len(segments):
                return segments[seg_index]
        
        return None
        
    except Exception as e:
        logger.debug(f"Failed to find matching segment: {str(e)}")
        return None


def format_srt_timestamp(seconds: float) -> str:
    """
    Format seconds as SRT timestamp (HH:MM:SS,mmm).
    
    Args:
        seconds: Time in seconds
        
    Returns:
        Formatted SRT timestamp
    """
    try:
        # Convert to timedelta
        td = timedelta(seconds=seconds)
        
        # Extract components
        hours = int(td.total_seconds() // 3600)
        minutes = int((td.total_seconds() % 3600) // 60)
        secs = int(td.total_seconds() % 60)
        milliseconds = int((seconds % 1) * 1000)
        
        # Format as SRT timestamp
        return f"{hours:02d}:{minutes:02d}:{secs:02d},{milliseconds:03d}"
        
    except Exception as e:
        logger.error(f"❌ Failed to format SRT timestamp: {str(e)}")
        return "00:00:00,000"


def validate_timing_sequence(synchronized_transcriptions: List[Dict[str, Any]]) -> List[Dict[str, Any]]:
    """
    Validate and correct timing sequence to ensure proper SRT ordering.
    
    Args:
        synchronized_transcriptions: List of synchronized transcriptions
        
    Returns:
        List of validated and corrected transcriptions
    """
    logger.info("🔍 Validating timing sequence...")
    
    try:
        # Filter successful and synchronized transcriptions
        valid_transcriptions = [
            t for t in synchronized_transcriptions 
            if t.get("success", False) and t.get("synchronized", False)
        ]
        
        if not valid_transcriptions:
            logger.warning("⚠️  No valid transcriptions to validate")
            return synchronized_transcriptions
        
        # Sort by start time
        valid_transcriptions.sort(key=lambda x: x.get("start_time", 0))
        
        # Validate and fix overlaps
        corrected_transcriptions = []
        
        for i, transcription in enumerate(valid_transcriptions):
            corrected = transcription.copy()
            
            # Check for overlap with previous segment
            if i > 0:
                prev_end = corrected_transcriptions[i-1]["end_time"]
                current_start = corrected["start_time"]
                
                if current_start < prev_end:
                    # Fix overlap by adjusting start time
                    gap = 0.1  # 100ms gap
                    corrected["start_time"] = prev_end + gap
                    
                    # Ensure end time is still after start time
                    if corrected["end_time"] <= corrected["start_time"]:
                        corrected["end_time"] = corrected["start_time"] + 1.0  # Minimum 1 second duration
                    
                    corrected["duration"] = corrected["end_time"] - corrected["start_time"]
                    logger.debug(f"Fixed overlap for segment {i}: adjusted start time to {corrected['start_time']:.2f}s")
            
            # Ensure minimum duration
            min_duration = 0.5  # 500ms minimum
            if corrected["duration"] < min_duration:
                corrected["end_time"] = corrected["start_time"] + min_duration
                corrected["duration"] = min_duration
                logger.debug(f"Fixed duration for segment {i}: extended to {min_duration}s")
            
            corrected_transcriptions.append(corrected)
        
        # Add back non-synchronized transcriptions
        for transcription in synchronized_transcriptions:
            if not (transcription.get("success", False) and transcription.get("synchronized", False)):
                corrected_transcriptions.append(transcription)
        
        logger.info(f"✅ Timing validation completed: {len(corrected_transcriptions)} transcriptions processed")
        
        return corrected_transcriptions
        
    except Exception as e:
        logger.error(f"❌ Timing validation failed: {str(e)}")
        return synchronized_transcriptions


def create_timing_report(synchronized_transcriptions: List[Dict[str, Any]], output_file: Path) -> None:
    """Create a timing report for debugging and validation."""
    try:
        valid_transcriptions = [
            t for t in synchronized_transcriptions 
            if t.get("success", False) and t.get("synchronized", False)
        ]
        
        report = {
            "total_transcriptions": len(synchronized_transcriptions),
            "synchronized_transcriptions": len(valid_transcriptions),
            "total_duration": 0,
            "segments": []
        }
        
        if valid_transcriptions:
            # Sort by start time
            valid_transcriptions.sort(key=lambda x: x.get("start_time", 0))
            
            for i, t in enumerate(valid_transcriptions):
                segment_info = {
                    "index": i,
                    "filename": t.get("filename", ""),
                    "start_time": t.get("start_time", 0),
                    "end_time": t.get("end_time", 0),
                    "duration": t.get("duration", 0),
                    "speaker": t.get("speaker", "unknown"),
                    "transcription_length": len(t.get("transcription", "")),
                    "srt_start": format_srt_timestamp(t.get("start_time", 0)),
                    "srt_end": format_srt_timestamp(t.get("end_time", 0))
                }
                report["segments"].append(segment_info)
            
            # Calculate total duration
            if valid_transcriptions:
                report["total_duration"] = valid_transcriptions[-1]["end_time"]
        
        # Save report
        with open(output_file, 'w', encoding='utf-8') as f:
            json.dump(report, f, indent=2, ensure_ascii=False)
        
        logger.info(f"📊 Timing report saved to: {output_file}")
        
    except Exception as e:
        logger.error(f"❌ Failed to create timing report: {str(e)}")


if __name__ == "__main__":
    # Test timing utilities
    logger.info("🧪 Testing timing utilities...")
    
    # Test SRT timestamp formatting
    test_times = [0, 1.5, 65.123, 3661.789]
    for t in test_times:
        formatted = format_srt_timestamp(t)
        logger.info(f"  {t}s -> {formatted}")
    
    logger.info("✅ Timing utilities test completed")
