[project]
name = "tts-data-pipeline"
version = "0.1.0"
description = "Add your description here"
readme = "README.md"
requires-python = "==3.10.*"
dependencies = [
    "colorama>=0.4.6",
    "cython>=3.1.2",
    "datasets>=2.19.2",
    "google-generativeai>=0.8.5",
    "huggingface-hub>=0.32.3",
    "jiwer>=3.1.0",
    "librosa>=0.10.1",
    "lightning>=2.5.1.post0",
    "llama-cpp-python>=0.3.9",
    "moviepy>=2.2.1",
    "numpy>=1.26.2",
    "packaging>=24.2",
    "pandas==2.1.3",
    "pathlib2>=2.3.7.post1",
    "pillow>=10.4.0",
    "pyannote-audio>=3.1.1",
    "pyarrow==15.0.2",
    "pydub>=0.25.1",
    "python-dotenv>=1.1.0",
    "pyyaml>=6.0.2",
    "resemble-enhance>=0.0.1",
    "sentencepiece>=0.2.0",
    "textgrid>=1.6.1",
    "tqdm>=4.66.1",
]
