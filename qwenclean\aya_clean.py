#!/usr/bin/env python3
"""
Vietnamese Text Dataset Cleaner - Aggressive Mode
Focuses on removing poor quality text with "better to kill by mistake than to miss" approach
"""

import pandas as pd
import json
import requests
from typing import Dict, Optional
import re
from pathlib import Path
import logging
import argparse
import sys
import warnings

# Suppress pandas warnings
warnings.filterwarnings('ignore', category=pd.errors.SettingWithCopyWarning)

# Setup logging
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(levelname)s - %(message)s')
logger = logging.getLogger(__name__)

class AggressiveVietnameseTextCleaner:
    def __init__(self, api_url="http://localhost:11434", model_name="aya-expanse-8b:q6_k", timeout=120, debug=False):
        """
        Initialize the aggressive text cleaner using Ollama API
        """
        self.api_url = api_url.rstrip('/')
        self.model_name = model_name
        self.timeout = timeout
        self.debug = debug
        self.session = requests.Session()
        self.debug_responses = []  # Store debug info
        
        # Set debug logging level
        if self.debug:
            logging.getLogger().setLevel(logging.DEBUG)
            logger.info("🐛 DEBUG MODE ENABLED")
        
        # Test connection and model
        logger.info(f"Connecting to Ollama API at {api_url}")
        self._test_connection()

    def _test_connection(self):
        """Test connection to Ollama API server"""
        try:
            # Test server connection
            response = self.session.get(f"{self.api_url}/api/tags", timeout=10)
            response.raise_for_status()
            
            # Check available models
            models = response.json().get('models', [])
            available_models = [model['name'] for model in models]
            
            logger.info(f"✅ Connected to Ollama API")
            logger.info(f"📦 Available models: {available_models}")
            
            if self.model_name not in available_models:
                logger.warning(f"⚠️ Model {self.model_name} not found, but will try to use it anyway")
                logger.info("💡 Make sure you've pulled the model: ollama pull aya-expanse-8b:q6_k")
            else:
                logger.info(f"✅ Model {self.model_name} is available")
            
            # Test model with a simple request
            test_response = self._call_api("generate", {
                "model": self.model_name,
                "prompt": "Xin chào",
                "stream": False,
                "options": {"num_predict": 5}
            })
            
            if test_response:
                logger.info("✅ Model test successful")
            else:
                logger.warning("⚠️ Model test failed, but continuing anyway")
                
        except requests.exceptions.ConnectionError:
            logger.error(f"❌ Could not connect to Ollama API at {self.api_url}")
            logger.error("💡 Make sure Ollama is running: ollama serve")
            sys.exit(1)
        except Exception as e:
            logger.error(f"❌ Connection test failed: {e}")
            logger.warning("⚠️ Continuing anyway...")

    def _call_api(self, endpoint: str, data: dict) -> Optional[dict]:
        """Make API call to Ollama server with debug support"""
        url = f"{self.api_url}/api/{endpoint}"
        
        if self.debug:
            logger.debug(f"🔗 API Request to {url}")
            logger.debug(f"📤 Request data: {json.dumps(data, indent=2, ensure_ascii=False)}")
        
        try:
            response = self.session.post(
                url, 
                json=data, 
                timeout=self.timeout,
                headers={'Content-Type': 'application/json'}
            )
            response.raise_for_status()
            
            result = response.json()
            
            if self.debug:
                logger.debug(f"📥 Response status: {response.status_code}")
                logger.debug(f"📥 Response data: {json.dumps(result, indent=2, ensure_ascii=False)}")
                
                # Store debug info
                debug_entry = {
                    'url': url,
                    'request': data,
                    'response': result,
                    'status_code': response.status_code,
                    'timestamp': pd.Timestamp.now().isoformat()
                }
                self.debug_responses.append(debug_entry)
            
            return result
            
        except requests.exceptions.Timeout:
            logger.error(f"⏰ API call timed out after {self.timeout}s")
            if self.debug:
                logger.debug(f"🐛 Timeout details: URL={url}, timeout={self.timeout}")
            return None
        except requests.exceptions.RequestException as e:
            logger.error(f"❌ API call failed: {e}")
            if self.debug:
                logger.debug(f"🐛 Request exception details: {type(e).__name__}: {e}")
            return None

    def aggressive_text_analysis(self, text: str) -> Dict[str, any]:
        """
        Aggressively analyze and clean Vietnamese text using Aya model
        Philosophy: "Better to kill by mistake than to miss"
        """
        
        system_prompt = """You are an expert Vietnamese text quality analyst with an AGGRESSIVE filtering approach. Your philosophy is "better to kill by mistake than to miss" (giết nhầm còn hơn bỏ sót).

<role>
You are a strict Vietnamese text quality filter. You MUST make binary decisions: KEEP or REMOVE each text sample.
</role>

<instructions>
1. ANALYZE the Vietnamese text for quality issues
2. DECIDE: Either "KEEP" (if salvageable) or "REMOVE" (if poor quality)  
3. CLEAN: If keeping, fix spelling, grammar, and punctuation errors
4. RESPOND: You MUST respond with valid JSON in the exact format specified below

CRITICAL REMOVAL CRITERIA (Mark as "REMOVE"):
- Spelling errors in >20% of words
- Excessive unnecessary foreign/English words (>30% of content)
- Meaningless, nonsensical, or incomprehensible text
- Too short (<10 words) AND lacks meaning
- Mixed language that's difficult to understand
- Spam, advertisements, or garbage content
- Corrupted or severely garbled text

KEEP CRITERIA (Mark as "KEEP"):
- Clear, meaningful Vietnamese text
- Necessary borrowed words (internet, email, smartphone, wifi, etc.)
- Fixable minor errors in otherwise good text
- Proper Vietnamese sentence structure
</instructions>

<output_format>
You MUST respond with ONLY valid JSON in this EXACT format. Do NOT include any other text:

{
  "decision": "KEEP",
  "cleaned_text": "corrected Vietnamese text here",
  "original_text": "original input text",
  "quality_score": 0.75,
  "issues_found": ["spelling errors", "grammar issues"],
  "reason": "explanation in Vietnamese why kept/removed",
  "spelling_errors": 3,
  "borrowed_words_count": 2,
  "meaningfulness": "clear"
}

The "decision" field must be exactly "KEEP" or "REMOVE".
The "meaningfulness" field must be exactly "clear", "unclear", or "nonsense".
</output_format>

<constraints>
- You MUST be aggressive in filtering - when in doubt, REMOVE
- You MUST preserve necessary technical terms and proper nouns
- You MUST respond with valid JSON only
- You MUST provide a Vietnamese explanation in the "reason" field
- Quality scores should range from 0.0 to 1.0
</constraints>

Remember: Your goal is aggressive quality control. Better to reject borderline cases than accept poor quality text.
"""
        # Call Aya model
        api_request = {
            "model": self.model_name,
            "messages": [
                {"role": "system", "content": system_prompt},
                {"role": "user", "content": f"Phân tích văn bản này:\n\n{text}"}
            ],
            "stream": False,
            "format": "json",
            "options": {
                "temperature": 0.1,
                "num_predict": 1000,
                "top_p": 0.9
            }
        }

        response = self._call_api("chat", api_request)
        
        if not response:
            return {
                'decision': 'KEEP',  # Conservative fallback
                'cleaned_text': text,
                'original_text': text,
                'quality_score': 0.5,
                'issues_found': ['API_ERROR'],
                'reason': 'Lỗi API - giữ nguyên văn bản',
                'spelling_errors': 0,
                'borrowed_words_count': 0,
                'meaningfulness': 'unclear'
            }
        
        # Parse JSON response
        try:
            content = response.get('message', {}).get('content', '{}')
            result = json.loads(content.strip())
            
            # Validate required fields
            if 'decision' not in result:
                result['decision'] = 'KEEP'
            if 'cleaned_text' not in result:
                result['cleaned_text'] = text
            if 'quality_score' not in result:
                result['quality_score'] = 0.5
            
            return result
            
        except json.JSONDecodeError as e:
            logger.warning(f"Failed to parse JSON response: {e}")
            logger.debug(f"Raw response: {content[:500]}...")
            
            # Try to extract decision manually if JSON fails
            decision = 'KEEP'
            cleaned_text = text
            
            if 'REMOVE' in content.upper():
                decision = 'REMOVE'
                cleaned_text = ''
            elif '"cleaned_text"' in content:
                try:
                    match = re.search(r'"cleaned_text":\s*"([^"]*)"', content)
                    if match:
                        cleaned_text = match.group(1)
                except:
                    pass
            
            return {
                'decision': decision,
                'cleaned_text': cleaned_text,
                'original_text': text,
                'quality_score': 0.3,
                'issues_found': ['JSON_PARSE_ERROR'],
                'reason': 'Lỗi phân tích JSON',
                'spelling_errors': 0,
                'borrowed_words_count': 0,
                'meaningfulness': 'unclear'
            }

    def process_text_row(self, df: pd.DataFrame, idx: int) -> None:
        """
        Process a single text row with aggressive cleaning
        """
        text = df.loc[idx, 'text'] if 'text' in df.columns else ''
        
        if self.debug:
            logger.debug(f"🔄 Processing row {idx}")
            logger.debug(f"📝 Original text: '{text}'")
        
        # Skip if text is empty or too short
        if not text or len(text.strip()) < 5:
            if self.debug:
                logger.debug(f"⏭️ Skipping row {idx}: text too short or empty")
            df.loc[idx, 'decision'] = 'REMOVE'
            df.loc[idx, 'reason'] = 'Văn bản quá ngắn hoặc trống'
            df.loc[idx, 'quality_score'] = 0.0
            return
        
        try:
            if self.debug:
                logger.debug(f"🔍 Analyzing row {idx}: {text[:50]}...")
            else:
                logger.debug(f"Processing: {text[:50]}...")
            
            # Aggressive analysis
            result = self.aggressive_text_analysis(text)
            
            if self.debug:
                logger.debug(f"📊 Analysis result for row {idx}:")
                logger.debug(f"  Decision: {result['decision']}")
                logger.debug(f"  Quality: {result['quality_score']}")
                logger.debug(f"  Reason: {result['reason']}")
                logger.debug(f"  Issues: {result.get('issues_found', [])}")
            
            # Update DataFrame with results
            df.loc[idx, 'decision'] = result['decision']
            df.loc[idx, 'cleaned_text'] = result['cleaned_text']
            df.loc[idx, 'original_text'] = result['original_text']
            df.loc[idx, 'quality_score'] = result['quality_score']
            df.loc[idx, 'issues_found'] = str(result.get('issues_found', []))
            df.loc[idx, 'reason'] = result['reason']
            df.loc[idx, 'spelling_errors'] = result.get('spelling_errors', 0)
            df.loc[idx, 'borrowed_words_count'] = result.get('borrowed_words_count', 0)
            df.loc[idx, 'meaningfulness'] = result.get('meaningfulness', 'unclear')
            df.loc[idx, 'model_used'] = self.model_name
            
            # Update the text column based on decision
            if result['decision'] == 'REMOVE':
                df.loc[idx, 'text'] = ''  # Mark for removal
                if self.debug:
                    logger.debug(f"❌ Row {idx} REMOVED: {result['reason']}")
                else:
                    logger.debug(f"❌ REMOVED: {result['reason']}")
            else:
                df.loc[idx, 'text'] = result['cleaned_text']
                if self.debug:
                    logger.debug(f"✅ Row {idx} KEPT: Quality score {result['quality_score']:.2f}")
                    if result['cleaned_text'] != text:
                        logger.debug(f"🔧 Text was cleaned: '{result['cleaned_text']}'")
                else:
                    logger.debug(f"✅ KEPT: Quality score {result['quality_score']:.2f}")
            
        except Exception as e:
            logger.error(f"Error processing row {idx}: {e}")
            if self.debug:
                logger.debug(f"🐛 Exception details for row {idx}: {type(e).__name__}: {e}")
                logger.debug(f"🐛 Text that caused error: '{text}'")
            df.loc[idx, 'decision'] = 'KEEP'  # Conservative on error
            df.loc[idx, 'reason'] = f'Lỗi xử lý: {str(e)}'
            df.loc[idx, 'quality_score'] = 0.0

    def clean_dataset(self, input_path: str, output_path: str, batch_size: int = 10):
        """
        Aggressively clean the entire text dataset
        """
        logger.info(f"Loading dataset from {input_path}")
        df = pd.read_parquet(input_path)
        
        # Make a copy to avoid view-related issues
        df = df.copy()
        
        logger.info(f"Dataset shape: {df.shape}")
        logger.info(f"Columns: {df.columns.tolist()}")
        
        # In debug mode, limit to smaller sample
        if self.debug:
            original_size = len(df)
            df = df.head(20)  # Only process first 20 rows in debug mode
            logger.info(f"🐛 DEBUG MODE: Processing only {len(df)} rows out of {original_size}")
        
        # Add analysis columns
        df['original_text'] = df.get('text', '').copy() if 'text' in df.columns else ''
        df['decision'] = ''
        df['cleaned_text'] = ''
        df['quality_score'] = 0.0
        df['issues_found'] = ''
        df['reason'] = ''
        df['spelling_errors'] = 0
        df['borrowed_words_count'] = 0
        df['meaningfulness'] = ''
        df['model_used'] = ''
        
        # Process rows
        total_rows = len(df)
        processed_count = 0
        
        logger.info(f"Starting aggressive cleaning of {total_rows} rows using {self.model_name}")
        logger.info("🔥 Philosophy: 'Giết nhầm còn hơn bỏ sót'")
        
        if self.debug:
            logger.info(f"🐛 Debug info will be saved to debug files")
        
        for i in range(0, total_rows, batch_size):
            batch_end = min(i + batch_size, total_rows)
            
            if self.debug:
                logger.info(f"🔄 Processing DEBUG batch {i//batch_size + 1}: rows {i} to {batch_end-1}")
            else:
                logger.info(f"Processing batch {i//batch_size + 1}: rows {i} to {batch_end-1}")
            
            for idx in range(i, batch_end):
                if idx >= total_rows:
                    break
                    
                try:
                    self.process_text_row(df, idx)
                    processed_count += 1
                    
                    if self.debug:
                        if processed_count % 5 == 0:  # More frequent updates in debug
                            logger.info(f"🐛 DEBUG: Processed {processed_count}/{total_rows} rows")
                    elif processed_count % 50 == 0:
                        logger.info(f"Processed {processed_count}/{total_rows} rows")
                        
                except Exception as e:
                    logger.error(f"Error processing row {idx}: {e}")
                    if self.debug:
                        logger.debug(f"🐛 Continuing after error in row {idx}")
                    continue
        
        # Filter out removed texts
        original_count = len(df)
        kept_df = df[df['decision'] == 'KEEP'].copy()
        removed_count = original_count - len(kept_df)
        
        logger.info(f"🔥 AGGRESSIVE FILTERING COMPLETE:")
        logger.info(f"📊 Original: {original_count} rows")
        logger.info(f"✅ Kept: {len(kept_df)} rows")
        logger.info(f"❌ Removed: {removed_count} rows ({removed_count/original_count:.1%})")
        
        # Save cleaned dataset (only kept rows)
        output_suffix = '.debug' if self.debug else ''
        final_output = str(Path(output_path).with_suffix(f'{output_suffix}.parquet'))
        
        logger.info(f"Saving cleaned dataset to {final_output}")
        kept_df.to_parquet(final_output, compression='snappy')
        
        # Save full analysis for review
        analysis_path = str(Path(output_path).with_suffix(f'{output_suffix}.analysis.parquet'))
        df.to_parquet(analysis_path, compression='snappy')
        logger.info(f"Full analysis saved to {analysis_path}")
        
        # Save debug information
        if self.debug and self.debug_responses:
            debug_path = str(Path(output_path).with_suffix('.debug_api_calls.json'))
            with open(debug_path, 'w', encoding='utf-8') as f:
                json.dump(self.debug_responses, f, ensure_ascii=False, indent=2)
            logger.info(f"🐛 Debug API calls saved to {debug_path}")
            
            # Save detailed debug log
            debug_summary = {
                'total_api_calls': len(self.debug_responses),
                'model_used': self.model_name,
                'api_url': self.api_url,
                'debug_mode': True,
                'rows_processed': processed_count,
                'batch_size': batch_size,
                'sample_requests': self.debug_responses[:3] if len(self.debug_responses) >= 3 else self.debug_responses,
                'processing_stats': {
                    'kept': len(kept_df),
                    'removed': removed_count,
                    'removal_rate': removed_count/original_count if original_count > 0 else 0
                }
            }
            
            debug_summary_path = str(Path(output_path).with_suffix('.debug_summary.json'))
            with open(debug_summary_path, 'w', encoding='utf-8') as f:
                json.dump(debug_summary, f, ensure_ascii=False, indent=2)
            logger.info(f"🐛 Debug summary saved to {debug_summary_path}")
        
        # Generate summary report
        avg_quality = kept_df['quality_score'].mean() if len(kept_df) > 0 else 0.0
        quality_distribution = kept_df['quality_score'].describe() if len(kept_df) > 0 else None
        
        summary = {
            'original_rows': original_count,
            'kept_rows': len(kept_df),
            'removed_rows': removed_count,
            'removal_rate': float(removed_count / original_count) if original_count > 0 else 0.0,
            'average_quality_score': float(avg_quality),
            'model_used': self.model_name,
            'api_url': self.api_url,
            'debug_mode': self.debug,
            'philosophy': 'Giết nhầm còn hơn bỏ sót',
            'removal_reasons': df['reason'].value_counts().head(10).to_dict() if 'reason' in df.columns else {},
            'meaningfulness_distribution': df['meaningfulness'].value_counts().to_dict() if 'meaningfulness' in df.columns else {}
        }
        
        if quality_distribution is not None:
            summary['quality_distribution'] = {
                'min': float(quality_distribution['min']),
                'max': float(quality_distribution['max']),
                'mean': float(quality_distribution['mean']),
                'std': float(quality_distribution['std'])
            }
        
        summary_suffix = '.debug' if self.debug else ''
        summary_path = str(Path(output_path).with_suffix(f'{summary_suffix}.json'))
        with open(summary_path, 'w', encoding='utf-8') as f:
            json.dump(summary, f, ensure_ascii=False, indent=2)
        
        if self.debug:
            logger.info(f"🐛 DEBUG MODE summary:")
            logger.info(f"  • API calls made: {len(self.debug_responses)}")
            logger.info(f"  • Files created: {final_output}, {analysis_path}, {summary_path}")
            
        logger.info(f"✅ Aggressive cleaning complete!")
        logger.info(f"📈 Average quality score: {avg_quality:.2f}")
        logger.info(f"🔥 Removal rate: {removed_count/original_count:.1%}" if original_count > 0 else "🔥 No data to process")
        
        return summary

def main():
    """
    Main execution function - Aggressive Vietnamese text cleaning
    """
    parser = argparse.ArgumentParser(description="Aggressive Vietnamese Text Cleaner")
    parser.add_argument("--input", "-i", required=True, help="Input parquet file")
    parser.add_argument("--output", "-o", required=True, help="Output parquet file (cleaned)")
    parser.add_argument("--batch-size", "-b", type=int, default=10, help="Batch size for processing")
    parser.add_argument("--api-url", default="http://localhost:11434", help="Ollama API URL")
    parser.add_argument("--model", "-m", default="aya-expanse-8b:q6_k", help="Model name")
    parser.add_argument("--timeout", "-t", type=int, default=120, help="API timeout in seconds")
    parser.add_argument("--debug", "-d", action="store_true", help="Enable debug mode (processes only 20 rows, detailed logging)")
    
    args = parser.parse_args()
    
    try:
        print(f"🔥 AGGRESSIVE Vietnamese Text Cleaner")
        print(f"💀 Philosophy: 'Giết nhầm còn hơn bỏ sót'")
        if args.debug:
            print(f"🐛 DEBUG MODE ENABLED - Will process only 20 rows with detailed logging")
        print(f"📡 API URL: {args.api_url}")
        print(f"🤖 Model: {args.model}")
        print(f"📁 Input: {args.input}")
        print(f"📁 Output: {args.output}")
        print(f"⏱️ Timeout: {args.timeout}s")
        print(f"📦 Batch size: {args.batch_size}")
        
        # Initialize cleaner
        cleaner = AggressiveVietnameseTextCleaner(
            api_url=args.api_url,
            model_name=args.model,
            timeout=args.timeout,
            debug=args.debug
        )
        
        # Preview dataset
        df_preview = pd.read_parquet(args.input)
        print(f"\n📊 Dataset Preview:")
        print(f"Shape: {df_preview.shape}")
        if 'text' in df_preview.columns:
            sample_text = df_preview['text'].dropna().iloc[0] if not df_preview['text'].dropna().empty else "No text"
            print(f"Sample text: {str(sample_text)[:100]}...")
            
            # Show more samples in debug mode
            if args.debug and len(df_preview) > 1:
                print(f"\n🐛 DEBUG: Additional samples:")
                for i in range(1, min(4, len(df_preview))):
                    if not df_preview['text'].dropna().empty and i < len(df_preview['text'].dropna()):
                        debug_text = df_preview['text'].dropna().iloc[i]
                        print(f"  Sample {i+1}: {str(debug_text)[:80]}...")
        
        # Ask user to continue
        if args.debug:
            response = input(f"\n❓ Start DEBUG cleaning? (20 rows only) (y/n): ").strip().lower()
        else:
            response = input(f"\n❓ Start aggressive cleaning? This may remove {30}-{70}% of data! (y/n): ").strip().lower()
        
        if response != 'y':
            print("👋 Cleaning cancelled.")
            return
        
        # Clean dataset
        summary = cleaner.clean_dataset(
            input_path=args.input,
            output_path=args.output,
            batch_size=args.batch_size
        )
        
        print(f"\n🎉 AGGRESSIVE CLEANING COMPLETED!")
        if args.debug:
            print(f"🐛 DEBUG MODE - Results are limited to sample data")
        print(f"🤖 Model: {summary['model_used']}")
        print(f"📊 Results:")
        print(f"  • Original rows: {summary['original_rows']:,}")
        print(f"  • Kept rows: {summary['kept_rows']:,}")
        print(f"  • Removed rows: {summary['removed_rows']:,}")
        print(f"  • Removal rate: {summary['removal_rate']:.1%}")
        print(f"  • Average quality: {summary['average_quality_score']:.2f}")
        
        print(f"\n📋 Top removal reasons:")
        for reason, count in list(summary['removal_reasons'].items())[:5]:
            print(f"  • {reason}: {count} times")
            
        if args.debug:
            print(f"\n🐛 Debug files created:")
            print(f"  • Cleaned data: {args.output.replace('.parquet', '.debug.parquet')}")
            print(f"  • Full analysis: {args.output.replace('.parquet', '.debug.analysis.parquet')}")
            print(f"  • API calls log: {args.output.replace('.parquet', '.debug_api_calls.json')}")
            print(f"  • Debug summary: {args.output.replace('.parquet', '.debug_summary.json')}")
    
    except KeyboardInterrupt:
        print("\n👋 Goodbye!")
    except Exception as e:
        logger.error(f"Failed to run cleaning: {e}")
        print(f"\n❌ Error: {e}")
        print("\n💡 Troubleshooting:")
        print("1. Make sure Ollama is running: ollama serve")
        print("2. Pull the Aya model: ollama pull aya-expanse-8b:q6_k")
        print("3. Check the API URL")
        print("4. Verify your input file exists")
        print("5. Try increasing timeout with --timeout 180")
        if not args.debug:
            print("6. Try debug mode first: --debug")

if __name__ == "__main__":
    main()