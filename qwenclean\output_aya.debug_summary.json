{"total_api_calls": 13, "model_used": "hf.co/lmstudio-community/aya-expanse-8b-GGUF:Q6_K", "api_url": "http://localhost:11434", "debug_mode": true, "rows_processed": 20, "batch_size": 3, "sample_requests": [{"url": "http://localhost:11434/api/generate", "request": {"model": "hf.co/lmstudio-community/aya-expanse-8b-GGUF:Q6_K", "prompt": "<PERSON><PERSON> ch<PERSON>o", "stream": false, "options": {"num_predict": 5}}, "response": {"model": "hf.co/lmstudio-community/aya-expanse-8b-GGUF:Q6_K", "created_at": "2025-06-14T14:16:44.7138833Z", "response": "Xin chào! Có thể", "done": true, "done_reason": "length", "context": [255000, 255006, 207067, 189610, 255001, 255001, 255000, 255007, 207067, 189610, 8, 25123, 8314], "total_duration": 4293553700, "load_duration": 3725986100, "prompt_eval_count": 9, "prompt_eval_duration": 429057800, "eval_count": 5, "eval_duration": 136009800}, "status_code": 200, "timestamp": "2025-06-14T21:16:44.715885"}, {"url": "http://localhost:11434/api/chat", "request": {"model": "hf.co/lmstudio-community/aya-expanse-8b-GGUF:Q6_K", "messages": [{"role": "system", "content": "You are an expert Vietnamese text quality analyst with an AGGRESSIVE filtering approach. Your philosophy is \"better to kill by mistake than to miss\" (g<PERSON><PERSON><PERSON> nhầm còn hơn bỏ sót).\n\n<role>\nYou are a strict Vietnamese text quality filter. You MUST make binary decisions: KEEP or REMOVE each text sample.\n</role>\n\n<instructions>\n1. ANALYZE the Vietnamese text for quality issues\n2. DECIDE: Either \"KEEP\" (if salvageable) or \"REMOVE\" (if poor quality)  \n3. CLEAN: If keeping, fix spelling, grammar, and punctuation errors\n4. RESPOND: You MUST respond with valid JSON in the exact format specified below\n\nCRITICAL REMOVAL CRITERIA (Mark as \"REMOVE\"):\n- Spelling errors in >20% of words\n- Excessive unnecessary foreign/English words (>30% of content)\n- Meaningless, nonsensical, or incomprehensible text\n- Too short (<10 words) AND lacks meaning\n- Mixed language that's difficult to understand\n- Spam, advertisements, or garbage content\n- Corrupted or severely garbled text\n\nKEEP CRITERIA (Mark as \"KEEP\"):\n- Clear, meaningful Vietnamese text\n- Necessary borrowed words (internet, email, smartphone, wifi, etc.)\n- Fixable minor errors in otherwise good text\n- Proper Vietnamese sentence structure\n</instructions>\n\n<output_format>\nYou MUST respond with ONLY valid JSON in this EXACT format. Do NOT include any other text:\n\n{\n  \"decision\": \"KEEP\",\n  \"cleaned_text\": \"corrected Vietnamese text here\",\n  \"original_text\": \"original input text\",\n  \"quality_score\": 0.75,\n  \"issues_found\": [\"spelling errors\", \"grammar issues\"],\n  \"reason\": \"explanation in Vietnamese why kept/removed\",\n  \"spelling_errors\": 3,\n  \"borrowed_words_count\": 2,\n  \"meaningfulness\": \"clear\"\n}\n\nThe \"decision\" field must be exactly \"KEEP\" or \"REMOVE\".\nThe \"meaningfulness\" field must be exactly \"clear\", \"unclear\", or \"nonsense\".\n</output_format>\n\n<constraints>\n- You MUST be aggressive in filtering - when in doubt, REMOVE\n- You MUST preserve necessary technical terms and proper nouns\n- You MUST respond with valid JSON only\n- You MUST provide a Vietnamese explanation in the \"reason\" field\n- Quality scores should range from 0.0 to 1.0\n</constraints>\n\nRemember: Your goal is aggressive quality control. Better to reject borderline cases than accept poor quality text.\n"}, {"role": "user", "content": "<PERSON>ân tích văn bản này:\n\n là quấy r lắm trồng dừa nên là cho cái nghề là tràm dừa nữa tràm ra rồi pha khô rồi cái đem lột lợp nhòa vốn rồi đây"}], "stream": false, "format": "json", "options": {"temperature": 0.1, "num_predict": 1000, "top_p": 0.9}}, "response": {"model": "hf.co/lmstudio-community/aya-expanse-8b-GGUF:Q6_K", "created_at": "2025-06-14T14:16:53.4906122Z", "message": {"role": "assistant", "content": "{\n  \"decision\": \"REMOVE\",\n  \"original_text\": \"là quấy r lắm trồng dừa nên là cho cái nghề là tràm dừa nữa tràm ra rồi pha khô rồi cái đem lột lợp nhòa vốn rồi đây\",\n  \"cleaned_text\": null,\n  \"quality_score\": 0.1,\n  \"issues_found\": [\"nonsensical text\", \"unclear meaning\"],\n  \"reason\": \"Văn bản này không có ý nghĩa rõ ràng và chứa nhiều từ viết sai hoặc không hợp lý, không thể sửa chữa được để trở nên có ý nghĩa.\",\n  \"spelling_errors\": 5,\n  \"borrowed_words_count\": 0,\n  \"meaningfulness\": \"nonsense\"\n}"}, "done_reason": "stop", "done": true, "total_duration": 4933283200, "load_duration": 37301200, "prompt_eval_count": 629, "prompt_eval_duration": 374045000, "eval_count": 162, "eval_duration": 4518931100}, "status_code": 200, "timestamp": "2025-06-14T21:16:53.491612"}, {"url": "http://localhost:11434/api/chat", "request": {"model": "hf.co/lmstudio-community/aya-expanse-8b-GGUF:Q6_K", "messages": [{"role": "system", "content": "You are an expert Vietnamese text quality analyst with an AGGRESSIVE filtering approach. Your philosophy is \"better to kill by mistake than to miss\" (g<PERSON><PERSON><PERSON> nhầm còn hơn bỏ sót).\n\n<role>\nYou are a strict Vietnamese text quality filter. You MUST make binary decisions: KEEP or REMOVE each text sample.\n</role>\n\n<instructions>\n1. ANALYZE the Vietnamese text for quality issues\n2. DECIDE: Either \"KEEP\" (if salvageable) or \"REMOVE\" (if poor quality)  \n3. CLEAN: If keeping, fix spelling, grammar, and punctuation errors\n4. RESPOND: You MUST respond with valid JSON in the exact format specified below\n\nCRITICAL REMOVAL CRITERIA (Mark as \"REMOVE\"):\n- Spelling errors in >20% of words\n- Excessive unnecessary foreign/English words (>30% of content)\n- Meaningless, nonsensical, or incomprehensible text\n- Too short (<10 words) AND lacks meaning\n- Mixed language that's difficult to understand\n- Spam, advertisements, or garbage content\n- Corrupted or severely garbled text\n\nKEEP CRITERIA (Mark as \"KEEP\"):\n- Clear, meaningful Vietnamese text\n- Necessary borrowed words (internet, email, smartphone, wifi, etc.)\n- Fixable minor errors in otherwise good text\n- Proper Vietnamese sentence structure\n</instructions>\n\n<output_format>\nYou MUST respond with ONLY valid JSON in this EXACT format. Do NOT include any other text:\n\n{\n  \"decision\": \"KEEP\",\n  \"cleaned_text\": \"corrected Vietnamese text here\",\n  \"original_text\": \"original input text\",\n  \"quality_score\": 0.75,\n  \"issues_found\": [\"spelling errors\", \"grammar issues\"],\n  \"reason\": \"explanation in Vietnamese why kept/removed\",\n  \"spelling_errors\": 3,\n  \"borrowed_words_count\": 2,\n  \"meaningfulness\": \"clear\"\n}\n\nThe \"decision\" field must be exactly \"KEEP\" or \"REMOVE\".\nThe \"meaningfulness\" field must be exactly \"clear\", \"unclear\", or \"nonsense\".\n</output_format>\n\n<constraints>\n- You MUST be aggressive in filtering - when in doubt, REMOVE\n- You MUST preserve necessary technical terms and proper nouns\n- You MUST respond with valid JSON only\n- You MUST provide a Vietnamese explanation in the \"reason\" field\n- Quality scores should range from 0.0 to 1.0\n</constraints>\n\nRemember: Your goal is aggressive quality control. Better to reject borderline cases than accept poor quality text.\n"}, {"role": "user", "content": "<PERSON><PERSON> tích văn bản này:\n\n cũng là miếng dừa ni raa hết á"}], "stream": false, "format": "json", "options": {"temperature": 0.1, "num_predict": 1000, "top_p": 0.9}}, "response": {"model": "hf.co/lmstudio-community/aya-expanse-8b-GGUF:Q6_K", "created_at": "2025-06-14T14:16:57.1452726Z", "message": {"role": "assistant", "content": "{\n  \"decision\": \"REMOVE\",\n  \"original_text\": \"cũng là miếng dừa ni raa hết á\",\n  \"cleaned_text\": null,\n  \"quality_score\": 0.0,\n  \"issues_found\": [\"nonsensical text\"],\n  \"reason\": \"V<PERSON>n bản này không có ý nghĩa rõ ràng và dường như là một câu nói vô nghĩa, do đó nó bị loại bỏ theo tiêu chí chất lượng thấp.\",\n  \"spelling_errors\": 0,\n  \"borrowed_words_count\": 0,\n  \"meaningfulness\": \"nonsense\"\n}"}, "done_reason": "stop", "done": true, "total_duration": 3649660200, "load_duration": 18000500, "prompt_eval_count": 602, "prompt_eval_duration": 9999900, "eval_count": 131, "eval_duration": 3620161100}, "status_code": 200, "timestamp": "2025-06-14T21:16:57.146273"}], "processing_stats": {"kept": 11, "removed": 9, "removal_rate": 0.45}}