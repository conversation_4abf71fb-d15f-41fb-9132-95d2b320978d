# AutoSub with LLM Requirements
# Core dependencies for the AutoSub with LLM pipeline

# PyTorch and Audio Processing (Granite Speech requires PyTorch 2.4+ for torch.nn.attention)
torch>=2.4.0
torchaudio>=2.4.0
librosa>=0.10.0
pydub>=0.25.0
soundfile>=0.12.0

# IBM Granite Speech model dependencies
transformers>=4.40.0,<4.50.0
accelerate>=0.20.0
peft>=0.4.0

# LLM Dependencies
google-generativeai>=0.3.0
llama-cpp-python>=0.2.0

# Audio Processing and Enhancement
pyannote.audio>=3.1.0
resemble-enhance>=0.0.1

# Data Processing
pandas>=2.0.0
numpy>=1.24.0
pyyaml>=6.0

# Progress and Utilities
tqdm>=4.65.0
colorama>=0.4.6
pathlib2>=2.3.7

# Video Processing
moviepy>=1.0.3

# Optional but recommended
jupyter>=1.0.0
matplotlib>=3.7.0
seaborn>=0.12.0

# Development and Testing
pytest>=7.0.0
black>=23.0.0
flake8>=6.0.0
