#!/usr/bin/env python3
"""
AutoSub with LLM Setup Test Script

This script tests the installation and configuration of the AutoSub with LLM pipeline.
It checks for required dependencies, validates configuration, and tests model availability.

Usage:
    python test_autosub_setup.py
    python test_autosub_setup.py --config autosub-config.yaml
"""

import sys
import os
import logging
from pathlib import Path
import argparse
import yaml

# Setup logging
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(levelname)s - %(message)s'
)
logger = logging.getLogger(__name__)


def test_python_version():
    """Test Python version compatibility."""
    logger.info("🐍 Testing Python version...")
    
    version = sys.version_info
    if version.major == 3 and version.minor >= 8:
        logger.info(f"✅ Python {version.major}.{version.minor}.{version.micro} - Compatible")
        return True
    else:
        logger.error(f"❌ Python {version.major}.{version.minor}.{version.micro} - Requires Python 3.8+")
        return False


def test_core_dependencies():
    """Test core Python dependencies."""
    logger.info("📦 Testing core dependencies...")
    
    dependencies = {
        'torch': 'PyTorch',
        'torchaudio': 'TorchAudio', 
        'yaml': 'PyYAML',
        'pathlib': 'Pathlib',
        'logging': 'Logging',
        'argparse': 'Argparse',
        'json': 'JSON',
        'tempfile': 'Tempfile',
        'shutil': 'Shutil',
        'subprocess': 'Subprocess',
        'time': 'Time',
        'tqdm': 'TQDM',
        'pandas': 'Pandas',
        'numpy': 'NumPy'
    }
    
    missing = []
    for module, name in dependencies.items():
        try:
            __import__(module)
            logger.info(f"✅ {name}")
        except ImportError:
            logger.error(f"❌ {name} - Not installed")
            missing.append(name)
    
    return len(missing) == 0


def test_audio_dependencies():
    """Test audio processing dependencies."""
    logger.info("🎵 Testing audio dependencies...")
    
    dependencies = {
        'pydub': 'Pydub',
        'librosa': 'Librosa'
    }
    
    missing = []
    for module, name in dependencies.items():
        try:
            __import__(module)
            logger.info(f"✅ {name}")
        except ImportError:
            logger.warning(f"⚠️  {name} - Not installed (optional but recommended)")
            missing.append(name)
    
    return len(missing) == 0


def test_nemo_availability():
    """Test NVIDIA NeMo availability."""
    logger.info("🤖 Testing NVIDIA NeMo...")
    
    try:
        import nemo.collections.asr as nemo_asr
        logger.info("✅ NeMo ASR - Available")
        
        # Test model loading capability
        try:
            logger.info("🔄 Testing Canary model access...")
            # Just test if we can access the model info without loading
            model_name = "nvidia/canary-1b-flash"
            logger.info(f"✅ Canary model accessible: {model_name}")
            return True
        except Exception as e:
            logger.warning(f"⚠️  Canary model test failed: {str(e)}")
            return False
            
    except ImportError:
        logger.error("❌ NeMo ASR - Not installed")
        logger.info("   Install with: pip install nemo_toolkit[asr]")
        return False


def test_llm_dependencies():
    """Test LLM dependencies."""
    logger.info("🧠 Testing LLM dependencies...")
    
    # Test Gemini
    try:
        import google.generativeai as genai
        logger.info("✅ Google Generative AI - Available")
        gemini_available = True
    except ImportError:
        logger.warning("⚠️  Google Generative AI - Not installed")
        logger.info("   Install with: pip install google-generativeai")
        gemini_available = False
    
    # Test Llama CPP
    try:
        from llama_cpp import Llama
        logger.info("✅ Llama CPP Python - Available")
        llama_available = True
    except ImportError:
        logger.warning("⚠️  Llama CPP Python - Not installed")
        logger.info("   Install with: pip install llama-cpp-python")
        llama_available = False
    
    if not gemini_available and not llama_available:
        logger.error("❌ No LLM backends available - Install at least one")
        return False
    
    return True


def test_pipeline_modules():
    """Test pipeline module availability."""
    logger.info("🔧 Testing pipeline modules...")
    
    # Add current directory to path
    sys.path.insert(0, str(Path(__file__).parent))
    
    modules = {
        'pipeline.preprocess': 'Preprocessing',
        'pipeline.diarization': 'Diarization',
        'pipeline.utils.file': 'File utilities',
        'pipeline.utils.audio': 'Audio utilities',
        'pipeline.utils.video': 'Video utilities'
    }
    
    missing = []
    for module, name in modules.items():
        try:
            __import__(module)
            logger.info(f"✅ {name}")
        except ImportError as e:
            logger.warning(f"⚠️  {name} - Not available: {str(e)}")
            missing.append(name)
    
    return len(missing) == 0


def test_autosub_modules():
    """Test AutoSub-specific modules."""
    logger.info("🎬 Testing AutoSub modules...")
    
    modules = {
        'pipeline.transcribe_canary': 'Canary transcription',
        'pipeline.llm_postprocess': 'LLM post-processing',
        'pipeline.utils.srt_generator': 'SRT generation',
        'pipeline.utils.timing_utils': 'Timing utilities'
    }
    
    missing = []
    for module, name in modules.items():
        try:
            __import__(module)
            logger.info(f"✅ {name}")
        except ImportError as e:
            logger.warning(f"⚠️  {name} - Not available: {str(e)}")
            missing.append(name)
    
    return len(missing) == 0


def test_ffmpeg():
    """Test FFmpeg availability."""
    logger.info("🎥 Testing FFmpeg...")
    
    try:
        import subprocess
        result = subprocess.run(['ffmpeg', '-version'], capture_output=True, text=True)
        if result.returncode == 0:
            version_line = result.stdout.split('\n')[0]
            logger.info(f"✅ FFmpeg - {version_line}")
            return True
        else:
            logger.error("❌ FFmpeg - Not working properly")
            return False
    except FileNotFoundError:
        logger.error("❌ FFmpeg - Not found in PATH")
        logger.info("   Install FFmpeg and add to PATH")
        return False


def test_configuration(config_path: str):
    """Test configuration file."""
    logger.info(f"⚙️  Testing configuration: {config_path}")
    
    try:
        config_file = Path(config_path)
        if not config_file.exists():
            logger.error(f"❌ Configuration file not found: {config_path}")
            return False
        
        with open(config_file, 'r', encoding='utf-8') as f:
            config = yaml.safe_load(f)
        
        # Check required keys
        required_keys = ['huggingface_token', 'device', 'llm_provider']
        missing_keys = []
        
        for key in required_keys:
            if key not in config:
                missing_keys.append(key)
        
        if missing_keys:
            logger.error(f"❌ Missing required configuration keys: {missing_keys}")
            return False
        
        # Check LLM provider
        llm_provider = config.get('llm_provider', 'gemini')
        if llm_provider not in ['gemini', 'aya-expanse']:
            logger.error(f"❌ Invalid LLM provider: {llm_provider}")
            return False
        
        # Check API keys
        if llm_provider == 'gemini':
            if not config.get('gemini_api_key') and not os.getenv('GEMINI_API_KEY'):
                logger.warning("⚠️  Gemini API key not found in config or environment")
        
        logger.info("✅ Configuration file valid")
        return True
        
    except Exception as e:
        logger.error(f"❌ Configuration test failed: {str(e)}")
        return False


def test_gpu_availability():
    """Test GPU availability."""
    logger.info("🖥️  Testing GPU availability...")
    
    try:
        import torch
        
        if torch.cuda.is_available():
            gpu_count = torch.cuda.device_count()
            current_device = torch.cuda.current_device()
            gpu_name = torch.cuda.get_device_name(current_device)
            
            logger.info(f"✅ CUDA available - {gpu_count} GPU(s)")
            logger.info(f"   Current device: {current_device} ({gpu_name})")
            
            # Test memory
            memory_total = torch.cuda.get_device_properties(current_device).total_memory
            memory_gb = memory_total / (1024**3)
            logger.info(f"   GPU memory: {memory_gb:.1f} GB")
            
            return True
        else:
            logger.warning("⚠️  CUDA not available - Will use CPU")
            return False
            
    except Exception as e:
        logger.error(f"❌ GPU test failed: {str(e)}")
        return False


def main():
    """Main test function."""
    parser = argparse.ArgumentParser(description="Test AutoSub with LLM setup")
    parser.add_argument(
        "--config", "-c",
        default="autosub-config.yaml",
        help="Configuration file to test (default: autosub-config.yaml)"
    )
    
    args = parser.parse_args()
    
    logger.info("🧪 AutoSub with LLM Setup Test")
    logger.info("=" * 50)
    
    tests = [
        ("Python Version", test_python_version),
        ("Core Dependencies", test_core_dependencies),
        ("Audio Dependencies", test_audio_dependencies),
        ("NVIDIA NeMo", test_nemo_availability),
        ("LLM Dependencies", test_llm_dependencies),
        ("Pipeline Modules", test_pipeline_modules),
        ("AutoSub Modules", test_autosub_modules),
        ("FFmpeg", test_ffmpeg),
        ("GPU Availability", test_gpu_availability),
        ("Configuration", lambda: test_configuration(args.config))
    ]
    
    results = []
    for test_name, test_func in tests:
        logger.info(f"\n--- {test_name} ---")
        try:
            result = test_func()
            results.append((test_name, result))
        except Exception as e:
            logger.error(f"❌ {test_name} test crashed: {str(e)}")
            results.append((test_name, False))
    
    # Summary
    logger.info("\n" + "=" * 50)
    logger.info("📊 TEST SUMMARY")
    logger.info("=" * 50)
    
    passed = 0
    total = len(results)
    
    for test_name, result in results:
        status = "✅ PASS" if result else "❌ FAIL"
        logger.info(f"{status} - {test_name}")
        if result:
            passed += 1
    
    logger.info(f"\nResults: {passed}/{total} tests passed")
    
    if passed == total:
        logger.info("🎉 All tests passed! AutoSub with LLM is ready to use.")
        return 0
    else:
        logger.warning("⚠️  Some tests failed. Check the issues above before running AutoSub.")
        return 1


if __name__ == "__main__":
    sys.exit(main())
